import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { PageProps } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import { Mail, Shield, User, UserPlus } from 'lucide-react';

interface CreateUserProps extends PageProps {
    roles: Array<{
        id: number;
        name: string;
        display_name: string;
    }>;
}

export default function CreateUser({ auth, roles }: CreateUserProps) {
    const { data, setData, post, processing, errors, reset } = useForm({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        role: 'user',
        bio: '',
        skills: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/admin/users', {
            onSuccess: () => {
                reset();
            },
        });
    };

    return (
        <AppLayout>
            <Head title="Create User" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center gap-2">
                    <UserPlus className="h-6 w-6" />
                    <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200">Create New User</h1>
                </div>

                <div className="py-12">
                    <div className="mx-auto max-w-3xl sm:px-6 lg:px-8">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <User className="h-5 w-5" />
                                    User Information
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSubmit} className="space-y-6">
                                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="name">Full Name</Label>
                                            <Input
                                                id="name"
                                                type="text"
                                                value={data.name}
                                                onChange={(e) => setData('name', e.target.value)}
                                                placeholder="Enter full name"
                                                required
                                            />
                                            {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="email">Email Address</Label>
                                            <div className="relative">
                                                <Mail className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                                <Input
                                                    id="email"
                                                    type="email"
                                                    value={data.email}
                                                    onChange={(e) => setData('email', e.target.value)}
                                                    placeholder="Enter email address"
                                                    className="pl-10"
                                                    required
                                                />
                                            </div>
                                            {errors.email && <p className="text-sm text-red-600">{errors.email}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="password">Password</Label>
                                            <Input
                                                id="password"
                                                type="password"
                                                value={data.password}
                                                onChange={(e) => setData('password', e.target.value)}
                                                placeholder="Enter password"
                                                required
                                            />
                                            {errors.password && <p className="text-sm text-red-600">{errors.password}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="password_confirmation">Confirm Password</Label>
                                            <Input
                                                id="password_confirmation"
                                                type="password"
                                                value={data.password_confirmation}
                                                onChange={(e) => setData('password_confirmation', e.target.value)}
                                                placeholder="Confirm password"
                                                required
                                            />
                                            {errors.password_confirmation && <p className="text-sm text-red-600">{errors.password_confirmation}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="role">User Role</Label>
                                            <Select value={data.role} onValueChange={(value) => setData('role', value)}>
                                                <SelectTrigger>
                                                    <div className="flex items-center gap-2">
                                                        <Shield className="h-4 w-4" />
                                                        <SelectValue placeholder="Select a role" />
                                                    </div>
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {roles.map((role) => (
                                                        <SelectItem key={role.id} value={role.name}>
                                                            {role.display_name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            {errors.role && <p className="text-sm text-red-600">{errors.role}</p>}
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="bio">Biography (Optional)</Label>
                                        <Textarea
                                            id="bio"
                                            value={data.bio}
                                            onChange={(e) => setData('bio', e.target.value)}
                                            placeholder="Enter user biography..."
                                            rows={4}
                                        />
                                        {errors.bio && <p className="text-sm text-red-600">{errors.bio}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="skills">Skills (Optional)</Label>
                                        <Input
                                            id="skills"
                                            type="text"
                                            value={data.skills}
                                            onChange={(e) => setData('skills', e.target.value)}
                                            placeholder="Enter skills separated by commas"
                                        />
                                        <p className="text-sm text-gray-600">
                                            Separate multiple skills with commas (e.g., JavaScript, React, Node.js)
                                        </p>
                                        {errors.skills && <p className="text-sm text-red-600">{errors.skills}</p>}
                                    </div>

                                    <div className="flex gap-4 pt-6">
                                        <Button type="submit" disabled={processing} className="flex-1">
                                            {processing ? 'Creating User...' : 'Create User'}
                                        </Button>
                                        <Button type="button" variant="outline" onClick={() => reset()} className="flex-1">
                                            Reset Form
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
