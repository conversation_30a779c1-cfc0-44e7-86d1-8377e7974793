<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ratings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained()->onDelete('cascade');
            $table->foreignUuid('client_id')->constrained('users')->onDelete('cascade');
            $table->foreignUuid('freelancer_id')->constrained('users')->onDelete('cascade');
            $table->enum('rated_by', ['client', 'freelancer']);
            $table->enum('rated_for', ['client', 'freelancer']);
            $table->integer('rating')->unsigned()->between(1, 5);
            $table->text('review')->nullable();
            $table->timestamps();

            // Ensure one rating per project per participant
            $table->unique(['project_id', 'client_id', 'freelancer_id', 'rated_by']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ratings');
    }
};
