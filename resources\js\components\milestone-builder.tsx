import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Minus, Plus } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

interface Milestone {
    title: string;
    description: string;
    payment_percentage: number;
}

interface MilestoneBuilderProps {
    onMilestonesChange: (milestones: Milestone[]) => void;
    useCustomMilestones: boolean;
    onToggleCustom: (useCustom: boolean) => void;
    totalMilestones: number;
    onTotalMilestonesChange: (total: number) => void;
    errors?: any;
}

const defaultMilestoneTemplates = {
    'Computer Science': [
        { title: 'Chapter 1: Introduction', description: 'Background, problem statement, and objectives', payment_percentage: 12.5 },
        { title: 'Chapter 2: Literature Review', description: 'Review of existing research and related work', payment_percentage: 12.5 },
        { title: 'Chapter 3: Methodology', description: 'Research design and implementation approach', payment_percentage: 15 },
        { title: 'Chapter 4: Implementation', description: 'System development and coding', payment_percentage: 20 },
        { title: 'Chapter 5: Testing & Evaluation', description: 'System testing and performance evaluation', payment_percentage: 15 },
        { title: 'Chapter 6: Results & Discussion', description: 'Analysis of results and findings', payment_percentage: 12.5 },
        { title: 'Chapter 7: Conclusion', description: 'Summary and future recommendations', payment_percentage: 7.5 },
        { title: 'Final Submission', description: 'Complete project with documentation', payment_percentage: 5 },
    ],
    Nursing: [
        { title: 'Background & Problem Statement', description: 'Clinical context and research problem', payment_percentage: 15 },
        { title: 'Literature Review', description: 'Review of evidence-based practices', payment_percentage: 20 },
        { title: 'Methodology', description: 'Research design for clinical study', payment_percentage: 15 },
        { title: 'Data Collection', description: 'Patient data and clinical observations', payment_percentage: 20 },
        { title: 'Analysis & Findings', description: 'Statistical analysis and clinical outcomes', payment_percentage: 20 },
        { title: 'Conclusion & Recommendations', description: 'Clinical implications and practice recommendations', payment_percentage: 10 },
    ],
    Law: [
        { title: 'Case Study 1', description: 'First legal case analysis and precedents', payment_percentage: 25 },
        { title: 'Case Study 2', description: 'Second legal case analysis and comparison', payment_percentage: 25 },
        { title: 'Legal Framework Analysis', description: 'Statutory and regulatory analysis', payment_percentage: 25 },
        { title: 'Final Report & Recommendations', description: 'Complete legal opinion and recommendations', payment_percentage: 25 },
    ],
    'Business Administration': [
        { title: 'Business Plan Overview', description: 'Executive summary and company overview', payment_percentage: 15 },
        { title: 'Market Analysis', description: 'Industry and competitive analysis', payment_percentage: 20 },
        { title: 'Marketing Strategy', description: 'Marketing plan and customer acquisition', payment_percentage: 20 },
        { title: 'Financial Projections', description: 'Financial forecasts and budgeting', payment_percentage: 20 },
        { title: 'Implementation Plan', description: 'Operational plan and milestones', payment_percentage: 15 },
        { title: 'Risk Assessment & Conclusion', description: 'Risk management and final recommendations', payment_percentage: 10 },
    ],
    Engineering: [
        { title: 'Problem Definition & Scope', description: 'Engineering problem statement and scope', payment_percentage: 10 },
        { title: 'Research & Analysis', description: 'Technical research and feasibility study', payment_percentage: 15 },
        { title: 'Design & Modeling', description: 'Engineering design and computational modeling', payment_percentage: 25 },
        { title: 'Prototype Development', description: 'Build and test prototype/simulation', payment_percentage: 25 },
        { title: 'Testing & Validation', description: 'Performance testing and validation', payment_percentage: 15 },
        { title: 'Final Report & Documentation', description: 'Technical documentation and conclusions', payment_percentage: 10 },
    ],
};

export function MilestoneBuilder({
    onMilestonesChange,
    useCustomMilestones,
    onToggleCustom,
    totalMilestones,
    onTotalMilestonesChange,
    errors,
}: MilestoneBuilderProps) {
    const [milestones, setMilestones] = useState<Milestone[]>([]);
    const [selectedCategory, setSelectedCategory] = useState<string>('');

    const calculateTotalPercentage = useCallback(() => {
        return milestones.reduce((sum, milestone) => sum + (milestone.payment_percentage || 0), 0);
    }, [milestones]);

    const generateDefaultMilestones = useCallback((count: number) => {
        const defaultMilestones: Milestone[] = [];
        const percentage = 100 / count;

        for (let i = 1; i <= count; i++) {
            defaultMilestones.push({
                title: `Chapter ${i}`,
                description: `Complete Chapter ${i} of the project`,
                payment_percentage: percentage,
            });
        }

        return defaultMilestones;
    }, []);

    const applyTemplate = (category: string) => {
        const template = defaultMilestoneTemplates[category as keyof typeof defaultMilestoneTemplates];
        if (template) {
            setMilestones([...template]);
            onMilestonesChange([...template]);
        }
    };

    const addMilestone = () => {
        const newMilestones = [...milestones, { title: '', description: '', payment_percentage: 0 }];
        setMilestones(newMilestones);
        onMilestonesChange(newMilestones);
    };

    const removeMilestone = (index: number) => {
        const newMilestones = milestones.filter((_, i) => i !== index);
        setMilestones(newMilestones);
        onMilestonesChange(newMilestones);
    };

    const updateMilestone = (index: number, field: keyof Milestone, value: string | number) => {
        const newMilestones = [...milestones];
        newMilestones[index] = { ...newMilestones[index], [field]: value };
        setMilestones(newMilestones);
        onMilestonesChange(newMilestones);
    };

    const distributeRemainingPercentage = () => {
        const totalPercentage = calculateTotalPercentage();
        const remaining = 100 - totalPercentage;
        const emptyMilestones = milestones.filter((m) => m.payment_percentage === 0).length;

        if (emptyMilestones > 0 && remaining > 0) {
            const perMilestone = remaining / emptyMilestones;
            const newMilestones = milestones.map((milestone) =>
                milestone.payment_percentage === 0 ? { ...milestone, payment_percentage: Math.round(perMilestone * 10) / 10 } : milestone,
            );
            setMilestones(newMilestones);
            onMilestonesChange(newMilestones);
        }
    };

    // Initialize with default milestones
    useEffect(() => {
        if (!useCustomMilestones && milestones.length === 0) {
            const defaultMilestones = generateDefaultMilestones(totalMilestones);
            setMilestones(defaultMilestones);
            onMilestonesChange(defaultMilestones);
        }
    }, [useCustomMilestones, totalMilestones, generateDefaultMilestones, onMilestonesChange, milestones.length]);

    const totalPercentage = calculateTotalPercentage();
    const isValidTotal = Math.abs(totalPercentage - 100) < 0.1;

    return (
        <Card>
            <CardHeader>
                <CardTitle>Project Delivery Structure</CardTitle>
                <CardDescription>
                    Define how your project will be delivered. You can use traditional chapters or create custom milestones that fit your field of
                    study.
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                {/* Toggle between custom and default */}
                <div className="flex items-center gap-4">
                    <Button
                        type="button"
                        variant={!useCustomMilestones ? 'default' : 'outline'}
                        onClick={() => {
                            onToggleCustom(false);
                            const defaultMilestones = generateDefaultMilestones(totalMilestones);
                            setMilestones(defaultMilestones);
                            onMilestonesChange(defaultMilestones);
                        }}
                    >
                        Traditional Chapters
                    </Button>
                    <Button
                        type="button"
                        variant={useCustomMilestones ? 'default' : 'outline'}
                        onClick={() => {
                            onToggleCustom(true);
                            if (milestones.length === 0) {
                                setMilestones([{ title: '', description: '', payment_percentage: 0 }]);
                            }
                        }}
                    >
                        Custom Milestones
                    </Button>
                </div>

                {!useCustomMilestones ? (
                    /* Traditional Chapter Mode */
                    <div className="space-y-4">
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <div>
                                <Label htmlFor="total_milestones">Number of Chapters</Label>
                                <Select
                                    value={totalMilestones.toString()}
                                    onValueChange={(value) => {
                                        const count = parseInt(value);
                                        onTotalMilestonesChange(count);
                                        const defaultMilestones = generateDefaultMilestones(count);
                                        setMilestones(defaultMilestones);
                                        onMilestonesChange(defaultMilestones);
                                    }}
                                >
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {[...Array(10)].map((_, i) => (
                                            <SelectItem key={i + 1} value={(i + 1).toString()}>
                                                {i + 1} Chapter{i + 1 > 1 ? 's' : ''}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors?.total_milestones && <p className="text-sm text-red-500">{errors.total_milestones}</p>}
                            </div>
                        </div>
                        <div className="rounded-lg bg-blue-50 p-4">
                            <p className="text-sm text-blue-800">
                                <strong>Traditional Structure:</strong> Your project will be delivered in {totalMilestones} chapters, with each
                                chapter representing {(100 / totalMilestones).toFixed(1)}% of the total payment.
                            </p>
                        </div>
                    </div>
                ) : (
                    /* Custom Milestone Mode */
                    <div className="space-y-4">
                        {/* Template Selection */}
                        <div>
                            <Label>Quick Templates (Optional)</Label>
                            <Select
                                value={selectedCategory}
                                onValueChange={(value) => {
                                    setSelectedCategory(value);
                                    applyTemplate(value);
                                }}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Choose a template for your field" />
                                </SelectTrigger>
                                <SelectContent>
                                    {Object.keys(defaultMilestoneTemplates).map((category) => (
                                        <SelectItem key={category} value={category}>
                                            {category}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        {/* Milestone List */}
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <Label>Milestones</Label>
                                <div className="flex items-center gap-2">
                                    <Badge variant={isValidTotal ? 'default' : 'destructive'}>Total: {totalPercentage.toFixed(1)}%</Badge>
                                    {!isValidTotal && totalPercentage < 100 && (
                                        <Button type="button" size="sm" variant="outline" onClick={distributeRemainingPercentage}>
                                            Auto-fill remaining
                                        </Button>
                                    )}
                                </div>
                            </div>

                            {milestones.map((milestone, index) => (
                                <Card key={index} className="border-l-4 border-l-primary">
                                    <CardContent className="p-4">
                                        <div className="grid grid-cols-1 gap-4 md:grid-cols-12">
                                            <div className="md:col-span-4">
                                                <Label htmlFor={`milestone-${index}-title`}>Title</Label>
                                                <Input
                                                    id={`milestone-${index}-title`}
                                                    value={milestone.title}
                                                    onChange={(e) => updateMilestone(index, 'title', e.target.value)}
                                                    placeholder="e.g., Literature Review"
                                                />
                                            </div>
                                            <div className="md:col-span-5">
                                                <Label htmlFor={`milestone-${index}-description`}>Description</Label>
                                                <Textarea
                                                    id={`milestone-${index}-description`}
                                                    value={milestone.description}
                                                    onChange={(e) => updateMilestone(index, 'description', e.target.value)}
                                                    placeholder="Brief description of deliverables"
                                                    rows={2}
                                                />
                                            </div>
                                            <div className="md:col-span-2">
                                                <Label htmlFor={`milestone-${index}-percentage`}>Payment %</Label>
                                                <Input
                                                    id={`milestone-${index}-percentage`}
                                                    type="number"
                                                    min="0"
                                                    max="100"
                                                    step="0.1"
                                                    value={milestone.payment_percentage || ''}
                                                    onChange={(e) => updateMilestone(index, 'payment_percentage', parseFloat(e.target.value) || 0)}
                                                    placeholder="0.0"
                                                />
                                            </div>
                                            <div className="flex items-end md:col-span-1">
                                                <Button
                                                    type="button"
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => removeMilestone(index)}
                                                    disabled={milestones.length <= 1}
                                                >
                                                    <Minus className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}

                            <Button type="button" variant="outline" onClick={addMilestone} disabled={milestones.length >= 10} className="w-full">
                                <Plus className="mr-2 h-4 w-4" />
                                Add Milestone
                            </Button>

                            {errors?.milestones && <p className="text-sm text-red-500">{errors.milestones}</p>}

                            {!isValidTotal && (
                                <div className="rounded-lg bg-yellow-50 p-4">
                                    <p className="text-sm text-yellow-800">
                                        <strong>Note:</strong> Milestone percentages must total exactly 100%. Current total:{' '}
                                        {totalPercentage.toFixed(1)}%
                                    </p>
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
