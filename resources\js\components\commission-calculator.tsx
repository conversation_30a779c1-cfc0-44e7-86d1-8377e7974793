import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DollarSign, Minus, TrendingDown } from 'lucide-react';
import { useEffect, useMemo } from 'react';

interface CommissionBreakdown {
    grossAmount: number;
    commissionAmount: number;
    netAmount: number;
    commissionRate: number;
}

interface CommissionCalculatorProps {
    amount: number;
    onCalculationChange?: (breakdown: CommissionBreakdown) => void;
    showHeader?: boolean;
    compact?: boolean;
}

export function CommissionCalculator({ amount, onCalculationChange, showHeader = true, compact = false }: CommissionCalculatorProps) {
    const breakdown = useMemo(() => {
        const numericAmount = Number(amount) || 0;
        // TODO: Get dynamic commission rate from API
        const commissionRate = 30; // 30% commission rate - will be made dynamic
        const commissionAmount = Math.round(numericAmount * (commissionRate / 100) * 100) / 100;
        const netAmount = Math.round((numericAmount - commissionAmount) * 100) / 100;

        return {
            grossAmount: numericAmount,
            commissionAmount,
            netAmount,
            commissionRate,
        };
    }, [amount]);

    useEffect(() => {
        if (onCalculationChange) {
            onCalculationChange(breakdown);
        }
    }, [breakdown, onCalculationChange]);

    const formatCurrency = (value: number) => `₵${value.toFixed(2)}`;

    if (compact) {
        return (
            <div className="rounded-lg border bg-muted/50 p-3">
                <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                        <span className="text-muted-foreground">Your earnings:</span>
                        <span className="font-medium text-green-600">{formatCurrency(breakdown.netAmount)}</span>
                    </div>
                    <div className="flex justify-between">
                        <span className="text-muted-foreground">Platform fee (30%):</span>
                        <span className="font-medium text-red-600">-{formatCurrency(breakdown.commissionAmount)}</span>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <Card className="border-blue-200 bg-blue-50/50">
            {showHeader && (
                <CardHeader className="pb-3">
                    <CardTitle className="flex items-center gap-2 text-lg">
                        <DollarSign className="h-5 w-5 text-blue-600" />
                        Earnings Calculator
                    </CardTitle>
                    <CardDescription>See exactly how much you'll earn after our 30% platform commission</CardDescription>
                </CardHeader>
            )}
            <CardContent className="space-y-4">
                {/* Gross Amount */}
                <div className="flex items-center justify-between py-2">
                    <div className="flex items-center gap-2">
                        <div className="rounded-full bg-blue-100 p-1">
                            <DollarSign className="h-4 w-4 text-blue-600" />
                        </div>
                        <span className="font-medium">Bid Amount</span>
                    </div>
                    <span className="text-lg font-bold">{formatCurrency(breakdown.grossAmount)}</span>
                </div>

                {/* Commission Deduction */}
                <div className="flex items-center justify-between border-t py-2">
                    <div className="flex items-center gap-2">
                        <div className="rounded-full bg-red-100 p-1">
                            <Minus className="h-4 w-4 text-red-600" />
                        </div>
                        <span className="text-muted-foreground">Platform Fee (30%)</span>
                    </div>
                    <span className="font-semibold text-red-600">-{formatCurrency(breakdown.commissionAmount)}</span>
                </div>

                {/* Net Earnings */}
                <div className="flex items-center justify-between rounded-lg border-t bg-green-50 px-4 py-3">
                    <div className="flex items-center gap-2">
                        <div className="rounded-full bg-green-100 p-1">
                            <TrendingDown className="h-4 w-4 rotate-180 text-green-600" />
                        </div>
                        <span className="font-semibold text-green-800">Your Earnings</span>
                    </div>
                    <span className="text-xl font-bold text-green-600">{formatCurrency(breakdown.netAmount)}</span>
                </div>

                {breakdown.grossAmount > 0 && (
                    <div className="mt-3 text-center text-sm text-muted-foreground">
                        You'll receive {((breakdown.netAmount / breakdown.grossAmount) * 100).toFixed(0)}% of your bid amount
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
