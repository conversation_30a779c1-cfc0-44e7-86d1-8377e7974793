<?php

namespace App\Notifications;

use App\Models\Bid;
use App\Models\Project;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class BidSubmittedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public Bid $bid,
        public Project $project,
        public User $freelancer
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('New Proposal Received - '.$this->project->title)
            ->greeting('Hello '.$notifiable->name.'!')
            ->line('You have received a new proposal for your project "'.$this->project->title.'".')
            ->line('**Freelancer:** '.$this->freelancer->name)
            ->line('**Proposed Amount:** ₵'.number_format((float) $this->bid->amount, 2))
            ->line('**Delivery Time:** '.$this->bid->delivery_days.' days')
            ->line('**Proposal Preview:** '.substr($this->bid->proposal, 0, 150).(strlen($this->bid->proposal) > 150 ? '...' : ''))
            ->action('View Full Proposal', url('/projects/'.$this->project->slug))
            ->line('Review the full proposal and take action on your project dashboard.')
            ->line('Thank you for using Thesylink!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'bid_submitted',
            'bid_id' => $this->bid->id,
            'project_id' => $this->project->id,
            'project_title' => $this->project->title,
            'project_slug' => $this->project->slug,
            'freelancer_id' => $this->freelancer->id,
            'freelancer_name' => $this->freelancer->name,
            'bid_amount' => $this->bid->amount,
            'delivery_days' => $this->bid->delivery_days,
            'proposal_preview' => substr($this->bid->proposal, 0, 100),
            'action_url' => '/projects/'.$this->project->slug,
            'message' => $this->freelancer->name.' submitted a proposal for ₵'.number_format((float) $this->bid->amount, 2),
            'title' => 'New Proposal Received',
        ];
    }
}
