import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';

interface UserStats {
    total_users: number;
    new_users_this_month: number;
    active_users: number;
    verified_users: number;
    users_by_role: Record<string, number>;
}

interface ProjectStats {
    total_projects: number;
    open_projects: number;
    in_progress_projects: number;
    completed_projects: number;
    projects_by_category: Record<string, number>;
    projects_by_academic_level: Record<string, number>;
}

interface DetailedStatsProps {
    userStats: UserStats;
    projectStats: ProjectStats;
}

export default function DetailedStats({ userStats, projectStats }: DetailedStatsProps) {
    return (
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            {/* User Statistics */}
            <Card>
                <CardHeader>
                    <CardTitle>User Statistics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                        <div className="rounded-lg bg-green-50 p-3 text-center">
                            <p className="text-2xl font-bold text-green-600">{userStats.active_users}</p>
                            <p className="text-sm text-green-700">Active Users</p>
                        </div>
                        <div className="rounded-lg bg-blue-50 p-3 text-center">
                            <p className="text-2xl font-bold text-blue-600">{userStats.verified_users}</p>
                            <p className="text-sm text-blue-700">Verified Users</p>
                        </div>
                    </div>

                    <div>
                        <h4 className="mb-2 font-medium">Users by Role</h4>
                        <div className="space-y-2">
                            {Object.entries(userStats.users_by_role).map(([role, count]) => (
                                <div key={role} className="flex justify-between">
                                    <span className="capitalize">{role.replace('_', ' ')}</span>
                                    <Badge variant="outline">{count}</Badge>
                                </div>
                            ))}
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Project Statistics */}
            <Card>
                <CardHeader>
                    <CardTitle>Project Statistics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-3 sm:grid-cols-3 sm:gap-2">
                        <div className="rounded-lg bg-yellow-50 p-3 text-center">
                            <p className="text-lg font-bold text-yellow-600">{projectStats.open_projects}</p>
                            <p className="text-xs text-yellow-700">Open</p>
                        </div>
                        <div className="rounded-lg bg-blue-50 p-3 text-center">
                            <p className="text-lg font-bold text-blue-600">{projectStats.in_progress_projects}</p>
                            <p className="text-xs text-blue-700">In Progress</p>
                        </div>
                        <div className="rounded-lg bg-green-50 p-3 text-center">
                            <p className="text-lg font-bold text-green-600">{projectStats.completed_projects}</p>
                            <p className="text-xs text-green-700">Completed</p>
                        </div>
                    </div>

                    <div>
                        <h4 className="mb-2 font-medium">Top Categories</h4>
                        <div className="space-y-2">
                            {Object.entries(projectStats.projects_by_category)
                                .slice(0, 5)
                                .map(([category, count]) => (
                                    <div key={category} className="flex justify-between">
                                        <span className="capitalize">{category}</span>
                                        <Badge variant="outline">{count}</Badge>
                                    </div>
                                ))}
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
