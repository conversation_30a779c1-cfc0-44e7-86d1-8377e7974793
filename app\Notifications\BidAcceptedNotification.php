<?php

namespace App\Notifications;

use App\Models\Bid;
use App\Models\Project;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class BidAcceptedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public Bid $bid,
        public Project $project,
        public User $client
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Congratulations! Your Proposal has been Accepted')
            ->greeting('Hello '.$notifiable->name.'!')
            ->line('Great news! Your proposal for "'.$this->project->title.'" has been accepted!')
            ->line('**Client:** '.$this->client->name)
            ->line('**Accepted Amount:** ₵'.number_format((float) $this->bid->amount, 2))
            ->line('**Project Deadline:** '.$this->bid->delivery_days.' days')
            ->line('The project has been assigned to you and escrow has been created for milestone payments.')
            ->action('View Project Details', url('/projects/'.$this->project->slug))
            ->line('Next steps:')
            ->line('• Review the project milestones')
            ->line('• Start working on the first milestone')
            ->line('• Communicate with the client through the project dashboard')
            ->line('Congratulations and best of luck with your project!')
            ->salutation('Best regards,  
The Thesylink Team');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'bid_accepted',
            'bid_id' => $this->bid->id,
            'project_id' => $this->project->id,
            'project_title' => $this->project->title,
            'project_slug' => $this->project->slug,
            'client_id' => $this->client->id,
            'client_name' => $this->client->name,
            'bid_amount' => $this->bid->amount,
            'delivery_days' => $this->bid->delivery_days,
            'action_url' => '/projects/'.$this->project->slug,
            'message' => 'Your proposal for "'.$this->project->title.'" has been accepted!',
            'title' => 'Proposal Accepted! 🎉',
        ];
    }
}
