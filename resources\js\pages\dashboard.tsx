import ProjectCard from '@/components/project-card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type PageProps } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Plus } from 'lucide-react';
import { useEffect } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

interface Project {
    id: number;
    title: string;
    slug?: string;
    description: string;
    budget_min?: number;
    budget_max?: number;
    budget_type: 'fixed' | 'negotiable';
    deadline?: string;
    category?: string;
    academic_level?: string;
    status: 'open' | 'in_progress' | 'completed' | 'cancelled';
    file_count: number;
    created_at: string;
}

interface DashboardProps extends PageProps {
    projects: Project[];
    flash: {
        success?: string;
        error?: string;
        warning?: string;
        info?: string;
    };
}

export default function Dashboard({ projects = [], flash }: DashboardProps) {
    const { toast } = useToast();

    useEffect(() => {
        if (flash?.success) {
            toast({
                variant: 'success',
                title: 'Success!',
                description: flash.success,
            });
        }

        if (flash?.error) {
            toast({
                variant: 'error',
                title: 'Error!',
                description: flash.error,
            });
        }

        if (flash?.warning) {
            toast({
                variant: 'warning',
                title: 'Warning!',
                description: flash.warning,
            });
        }

        if (flash?.info) {
            toast({
                variant: 'info',
                title: 'Info',
                description: flash.info,
            });
        }
    }, [flash, toast]);
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                    <div className="min-w-0 flex-1">
                        <h1 className="text-2xl font-bold">My Projects</h1>
                        <p className="text-muted-foreground">Manage your posted projects and track their progress</p>
                    </div>
                    <div className="flex-shrink-0">
                        <Button asChild className="w-full sm:w-auto">
                            <Link href="/projects/create">
                                <Plus className="mr-2 h-4 w-4" />
                                Post New Project
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Projects Grid */}
                {projects.length > 0 ? (
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                        {projects.map((project) => (
                            <ProjectCard key={project.id} project={project} variant="dashboard" />
                        ))}
                    </div>
                ) : (
                    <div className="py-8 text-center sm:py-12">
                        <div className="mx-auto max-w-md px-4">
                            <div className="mx-auto h-12 w-12 text-muted-foreground/50">
                                <Plus className="h-full w-full" />
                            </div>
                            <h3 className="mt-4 text-lg font-semibold">No projects yet</h3>
                            <p className="mt-2 text-sm text-muted-foreground sm:text-base">
                                Get started by posting your first project. Connect with talented researchers and get your academic work done
                                professionally.
                            </p>
                            <Button className="mt-4 w-full sm:w-auto" asChild>
                                <Link href="/projects/create">
                                    <Plus className="mr-2 h-4 w-4" />
                                    Post Your First Project
                                </Link>
                            </Button>
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
