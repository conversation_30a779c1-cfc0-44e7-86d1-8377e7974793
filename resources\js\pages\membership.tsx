import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { Award, Check, Clock, Globe, Headphones, Shield, Users, Zap } from 'lucide-react';

interface PricingPlan {
    id: string;
    title: string;
    subtitle: string;
    price: string;
    period: string;
    description: string;
    features: string[];
    cta: string;
    popular?: boolean;
    icon: React.ComponentType<any>;
    gradientFrom: string;
    gradientTo: string;
}

// Freelancer Plans
const freelancerPlans: PricingPlan[] = [
    {
        id: 'freelancer-starter',
        title: 'Freelancer Starter',
        subtitle: 'Begin Your Journey',
        price: 'GHS 0',
        period: 'month',
        description: 'Start building your freelance career with basic access to projects.',
        features: [
            '10 Bids per Month',
            'Basic Profile Features',
            'Email Support',
            'Standard Project Access',
            'Basic Portfolio (3 items)',
            'Client Messaging',
        ],
        cta: 'Start Free',
        icon: Users,
        gradientFrom: 'from-green-500',
        gradientTo: 'to-emerald-500',
    },
    {
        id: 'freelancer-professional',
        title: 'Freelancer Pro',
        subtitle: 'Grow Your Business',
        price: 'GHS 40',
        period: 'month',
        description: 'Enhanced features to help you win more projects and build reputation.',
        features: [
            '50 Bids per Month',
            'Enhanced Profile Features',
            'Priority Support',
            'All Project Categories',
            'Extended Portfolio (10 items)',
            'Advanced Client Communication',
            'Bid Insights & Analytics',
            'Featured Profile Listing',
            'Skills Verification Badge',
        ],
        cta: 'Upgrade to Pro',
        popular: true,
        icon: Zap,
        gradientFrom: 'from-indigo-500',
        gradientTo: 'to-blue-500',
    },
    {
        id: 'freelancer-expert',
        title: 'Freelancer Expert',
        subtitle: 'Dominate Your Field',
        price: 'GHS 80',
        period: 'month',
        description: 'Maximum exposure and tools for established freelancers.',
        features: [
            'Unlimited Bids',
            'Premium Profile Features',
            '24/7 Priority Support',
            'Exclusive High-Value Projects',
            'Unlimited Portfolio Items',
            'VIP Client Communication',
            'Advanced Analytics Dashboard',
            'Top Freelancer Badge',
            'Custom Skills Certification',
            'Direct Client Invitations',
            'Revenue Optimization Tools',
        ],
        cta: 'Become Expert',
        icon: Award,
        gradientFrom: 'from-purple-600',
        gradientTo: 'to-indigo-600',
    },
];

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Membership Plans',
        href: '/membership',
    },
];

export default function Membership() {
    const { auth } = usePage<SharedData>().props;

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Membership Plans">
                <meta name="description" content="Choose the perfect membership plan for your academic journey or freelance career on Thesylink." />
            </Head>

            <div className="space-y-8">
                {/* Header */}
                <div className="text-center">
                    <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl dark:text-white">
                        Choose Your
                        <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent"> Perfect Plan</span>
                    </h1>
                    <p className="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300">
                        Choose the perfect freelancer membership plan to showcase your expertise and connect with academic opportunities.
                    </p>
                </div>

                {/* Freelancer Plans Section */}
                <div className="space-y-8">
                    <div className="text-center">
                        <div className="mb-4 flex items-center justify-center gap-2">
                            <Users className="h-8 w-8 text-secondary" />
                            <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">Freelancer Plans</h2>
                        </div>
                        <p className="text-lg text-gray-600 dark:text-gray-300">
                            Build your freelance career and connect with academic opportunities
                        </p>
                    </div>

                    <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
                        {freelancerPlans.map((plan) => (
                            <PricingCard key={plan.id} plan={plan} />
                        ))}
                    </div>
                </div>

                {/* Features Section */}
                <div className="space-y-8">
                    <div className="text-center">
                        <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">Why Choose Thesylink?</h2>
                        <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
                            Discover the benefits that make us the premier academic freelancing platform
                        </p>
                    </div>

                    <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
                        <FeatureCard
                            icon={Shield}
                            title="Secure Payments"
                            description="Escrow protection ensures safe transactions for both students and freelancers"
                            color="text-green-600"
                        />
                        <FeatureCard
                            icon={Globe}
                            title="Global Network"
                            description="Connect with academic experts from universities around the world"
                            color="text-blue-600"
                        />
                        <FeatureCard
                            icon={Clock}
                            title="24/7 Support"
                            description="Get help whenever you need it with our dedicated support team"
                            color="text-purple-600"
                        />
                        <FeatureCard
                            icon="Headphones"
                            title="Quality Assurance"
                            description="Every project goes through our quality review process"
                            color="text-orange-600"
                        />
                    </div>
                </div>

                {/* FAQ Section */}
                <div className="space-y-8">
                    <div className="text-center">
                        <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">Frequently Asked Questions</h2>
                    </div>

                    <div className="mx-auto max-w-3xl space-y-8">
                        <div className="border-b border-gray-200 pb-6 dark:border-gray-700">
                            <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">Can I change my plan anytime?</h3>
                            <p className="text-gray-600 dark:text-gray-300">
                                Yes! You can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.
                            </p>
                        </div>
                        <div className="border-b border-gray-200 pb-6 dark:border-gray-700">
                            <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">Do you offer refunds?</h3>
                            <p className="text-gray-600 dark:text-gray-300">
                                We offer a 30-day money-back guarantee for all paid plans. If you're not satisfied, contact our support team.
                            </p>
                        </div>
                        <div className="border-b border-gray-200 pb-6 dark:border-gray-700">
                            <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">Are there any setup fees?</h3>
                            <p className="text-gray-600 dark:text-gray-300">
                                No setup fees! You only pay for your chosen plan, and you can start using all features immediately.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}

// Pricing Card Component
function PricingCard({ plan }: { plan: PricingPlan }) {
    const IconComponent = plan.icon;

    return (
        <Card className={`relative p-6 ${plan.popular ? 'scale-105 shadow-xl ring-2 ring-primary' : ''} transition-all hover:shadow-lg`}>
            {plan.popular && (
                <div className="absolute -top-3 left-1/2 -translate-x-1/2 transform">
                    <Badge className="bg-primary px-4 py-1 text-white">Most Popular</Badge>
                </div>
            )}

            <div className="text-center">
                <div
                    className={`mx-auto h-16 w-16 rounded-full bg-gradient-to-r ${plan.gradientFrom} ${plan.gradientTo} mb-4 flex items-center justify-center`}
                >
                    <IconComponent className="h-8 w-8 text-white" />
                </div>

                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">{plan.title}</h3>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">{plan.subtitle}</p>

                <div className="mt-4 mb-2">
                    <span className="text-3xl font-bold text-gray-900 dark:text-white">{plan.price}</span>
                    <span className="text-gray-600 dark:text-gray-400">/{plan.period}</span>
                </div>

                <p className="mb-6 text-sm text-gray-600 dark:text-gray-300">{plan.description}</p>
            </div>

            <ul className="mb-6 space-y-3">
                {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm">
                        <Check className="mr-3 h-4 w-4 flex-shrink-0 text-green-500" />
                        <span className="text-gray-600 dark:text-gray-300">{feature}</span>
                    </li>
                ))}
            </ul>

            <div className="mt-auto">
                <Button
                    asChild
                    className={`w-full ${plan.popular ? 'bg-primary hover:bg-primary/90' : 'bg-gray-900 hover:bg-gray-800 dark:bg-white dark:text-gray-900 dark:hover:bg-gray-100'}`}
                >
                    <Link href={route('register')}>{plan.cta}</Link>
                </Button>
            </div>
        </Card>
    );
}

// Feature Card Component
function FeatureCard({
    icon: Icon,
    title,
    description,
    color,
}: {
    icon: React.ComponentType<any> | string;
    title: string;
    description: string;
    color: string;
}) {
    const IconComponent = typeof Icon === 'string' ? (Icon === 'Headphones' ? Headphones : Shield) : Icon;

    return (
        <div className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-800">
                <IconComponent className={`h-6 w-6 ${color}`} />
            </div>
            <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">{title}</h3>
            <p className="text-gray-600 dark:text-gray-300">{description}</p>
        </div>
    );
}
