<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Lara<PERSON>\Socialite\Facades\Socialite;
use Laravel\Socialite\Two\InvalidStateException;

class GoogleController extends Controller
{
    /**
     * Redirect to Google OAuth.
     */
    public function redirect(): RedirectResponse
    {
        // Redirect to Google OAuth
        return Socialite::driver('google')->redirect();
    }

    /**
     * Handle Google OAuth callback.
     */
    public function callback(Request $request): RedirectResponse
    {
        try {
            $googleUser = Socialite::driver('google')->user();

            // Check if user exists with this Google ID
            $user = User::where('google_id', $googleUser->getId())->first();

            if ($user) {
                Auth::login($user, true);
                $request->session()->regenerate();

                return redirect()->intended(route('dashboard', absolute: false));
            }

            // Check if user exists with this email
            $existingUser = User::where('email', $googleUser->getEmail())->first();

            if ($existingUser) {
                // Update the existing user with Google ID
                $existingUser->update([
                    'google_id' => $googleUser->getId(),
                ]);
                Auth::login($existingUser, true);
                $request->session()->regenerate();

                return redirect()->intended(route('dashboard', absolute: false));
            }

            // Create new user
            $newUser = User::create([
                'name' => $googleUser->getName(),
                'email' => $googleUser->getEmail(),
                'google_id' => $googleUser->getId(),
                'email_verified_at' => now(),
                'password' => null, // No password for Google OAuth users
            ]);

            Auth::login($newUser, true);
            $request->session()->regenerate();

            return redirect()->intended(route('dashboard', absolute: false));
        } catch (InvalidStateException $e) {
            Log::error('Google OAuth state validation failed', [
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'user_agent' => $request->userAgent(),
                'ip' => $request->ip(),
            ]);

            return redirect()->route('login')->with('error', 'Google authentication failed due to session issues. Please try again.');
        } catch (\Throwable $e) {
            Log::error('Google OAuth callback failed', [
                'exception' => get_class($e),
                'message' => $e->getMessage(),
            ]);

            return redirect()->route('login')->with('error', 'Google authentication failed. Please try again.');
        }
    }
}
