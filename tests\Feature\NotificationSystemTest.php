<?php

namespace Tests\Feature;

use App\Models\Bid;
use App\Models\Project;
use App\Models\User;
use App\Notifications\BidAcceptedNotification;
use App\Notifications\BidRejectedNotification;
use App\Notifications\BidSubmittedNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class NotificationSystemTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Notification::fake();
    }

    public function test_bid_submitted_notification_is_sent_to_project_owner(): void
    {
        $projectOwner = User::factory()->create();
        $freelancer = User::factory()->create();

        $project = Project::factory()->create(['user_id' => $projectOwner->id]);

        $this->actingAs($freelancer)
            ->post(route('bids.store', $project), [
                'amount' => 1000,
                'proposal' => 'This is a test proposal with more than 50 characters to meet the validation requirement.',
                'delivery_days' => 7,
            ]);

        Notification::assertSentTo($projectOwner, BidSubmittedNotification::class);
    }

    public function test_bid_accepted_notification_is_sent_to_freelancer(): void
    {
        $projectOwner = User::factory()->create();
        $freelancer = User::factory()->create();

        $project = Project::factory()->create([
            'user_id' => $projectOwner->id,
            'status' => 'open',
        ]);

        $bid = Bid::factory()->create([
            'project_id' => $project->id,
            'user_id' => $freelancer->id,
            'status' => 'pending',
        ]);

        $this->actingAs($projectOwner)
            ->patch(route('bids.accept', [$project, $bid]));

        Notification::assertSentTo($freelancer, BidAcceptedNotification::class);
    }

    public function test_bid_rejected_notification_is_sent_to_freelancer(): void
    {
        $projectOwner = User::factory()->create();
        $freelancer = User::factory()->create();

        $project = Project::factory()->create(['user_id' => $projectOwner->id]);
        $bid = Bid::factory()->create([
            'project_id' => $project->id,
            'user_id' => $freelancer->id,
            'status' => 'pending',
        ]);

        $this->actingAs($projectOwner)
            ->patch(route('bids.reject', [$project, $bid]));

        Notification::assertSentTo($freelancer, BidRejectedNotification::class);
    }

    public function test_notification_controller_returns_dropdown_notifications(): void
    {
        $user = User::factory()->create();

        // Create a fake notification
        $user->notifications()->create([
            'id' => '123e4567-e89b-12d3-a456-426614174000',
            'type' => BidSubmittedNotification::class,
            'data' => [
                'type' => 'bid_submitted',
                'title' => 'New Proposal Received',
                'message' => 'John Doe submitted a proposal',
                'action_url' => '/projects/test-project',
            ],
            'read_at' => null,
        ]);

        $response = $this->actingAs($user)
            ->get('/notifications/dropdown');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'unread_count' => 1,
            ]);
    }

    public function test_notification_can_be_marked_as_read(): void
    {
        $user = User::factory()->create();

        $notification = $user->notifications()->create([
            'id' => '123e4567-e89b-12d3-a456-426614174000',
            'type' => BidSubmittedNotification::class,
            'data' => [
                'type' => 'bid_submitted',
                'title' => 'New Proposal Received',
                'message' => 'John Doe submitted a proposal',
            ],
            'read_at' => null,
        ]);

        $response = $this->actingAs($user)
            ->patch("/notifications/{$notification->id}/read");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Notification marked as read.',
            ]);

        $this->assertNotNull($notification->fresh()->read_at);
    }
}
