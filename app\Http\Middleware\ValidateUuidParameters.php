<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class ValidateUuidParameters
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // List of route parameters that should be UUIDs
        $uuidParameters = ['user', 'id'];

        foreach ($uuidParameters as $param) {
            $value = $request->route($param);

            // If parameter exists and is not a valid UUID, return 404
            if ($value !== null && ! Str::isUuid($value)) {
                // Log the issue for debugging
                Log::info("UUID validation failed for parameter: {$param}, value: {$value}");
                abort(404, 'Invalid identifier');
            }
        }

        return $next($request);
    }
}
