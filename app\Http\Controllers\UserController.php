<?php

namespace App\Http\Controllers;

use App\Models\Bid;
use App\Models\Project;
use App\Models\User;
use App\Models\WalletTransaction;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class UserController extends Controller
{
    /**
     * Show the current user's profile.
     */
    public function profile()
    {
        $user = Auth::user();

        // Get user's project statistics
        $projectsPosted = Project::where('user_id', $user->id)->count();
        $projectsCompleted = Project::where('user_id', $user->id)
            ->where('status', 'completed')
            ->count();

        // Get user's bid statistics
        $totalBids = Bid::where('user_id', $user->id)->count();
        $acceptedBids = Bid::where('user_id', $user->id)
            ->where('status', 'accepted')
            ->count();

        // Get recent projects (as client)
        $recentProjectsPosted = Project::where('user_id', $user->id)
            ->with(['assignedFreelancer', 'bids'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get recent bids (as freelancer)
        $recentBids = Bid::where('user_id', $user->id)
            ->with(['project.client'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        return Inertia::render('profile/show', [
            'profileUser' => $user,
            'isOwnProfile' => Auth::id() === $user->id,
            'stats' => [
                'projects_posted' => $projectsPosted,
                'projects_completed' => $projectsCompleted,
                'total_bids' => $totalBids,
                'accepted_bids' => $acceptedBids,
                'success_rate_as_client' => $projectsPosted > 0 ? round(($projectsCompleted / $projectsPosted) * 100, 1) : 0,
                'success_rate_as_freelancer' => $totalBids > 0 ? round(($acceptedBids / $totalBids) * 100, 1) : 0,
            ],
            'recent_projects_posted' => $recentProjectsPosted,
            'recent_bids' => $recentBids,
        ]);
    }

    /**
     * Show analytics for the current user.
     */
    public function analytics()
    {
        $user = Auth::user();

        // Monthly earnings and spending
        $monthlyData = WalletTransaction::where('user_id', $user->id)
            ->selectRaw('TO_CHAR(created_at, \'YYYY-MM\') as month, 
                        SUM(CASE WHEN type = \'credit\' THEN amount ELSE 0 END) as earnings,
                        SUM(CASE WHEN type = \'debit\' THEN amount ELSE 0 END) as spending')
            ->groupBy('month')
            ->orderBy('month', 'desc')
            ->limit(12)
            ->get();

        // Project completion timeline
        $projectTimeline = Project::where('user_id', $user->id)
            ->selectRaw('TO_CHAR(created_at, \'YYYY-MM\') as month, 
                        COUNT(*) as total,
                        SUM(CASE WHEN status = \'completed\' THEN 1 ELSE 0 END) as completed')
            ->groupBy('month')
            ->orderBy('month', 'desc')
            ->limit(12)
            ->get();

        // Bid success rate over time
        $bidTimeline = Bid::where('user_id', $user->id)
            ->selectRaw('TO_CHAR(created_at, \'YYYY-MM\') as month, 
                        COUNT(*) as total_bids,
                        SUM(CASE WHEN status = \'accepted\' THEN 1 ELSE 0 END) as accepted_bids')
            ->groupBy('month')
            ->orderBy('month', 'desc')
            ->limit(12)
            ->get();

        // Overall statistics
        $totalEarned = WalletTransaction::where('user_id', $user->id)
            ->where('type', 'credit')
            ->sum('amount');

        $totalSpent = WalletTransaction::where('user_id', $user->id)
            ->where('type', 'debit')
            ->sum('amount');

        return Inertia::render('analytics/show', [
            'user' => $user,
            'monthly_data' => $monthlyData,
            'project_timeline' => $projectTimeline,
            'bid_timeline' => $bidTimeline,
            'summary' => [
                'total_earned' => $totalEarned,
                'total_spent' => $totalSpent,
                'net_balance' => $totalEarned - $totalSpent,
                'projects_posted' => Project::where('user_id', $user->id)->count(),
                'projects_completed' => Project::where('user_id', $user->id)->where('status', 'completed')->count(),
                'total_bids' => Bid::where('user_id', $user->id)->count(),
                'accepted_bids' => Bid::where('user_id', $user->id)->where('status', 'accepted')->count(),
            ],
        ]);
    }
}
