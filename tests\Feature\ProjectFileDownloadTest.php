<?php

namespace Tests\Feature;

use App\Models\Project;
use App\Models\ProjectFile;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ProjectFileDownloadTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');
    }

    public function test_project_owner_can_download_files(): void
    {
        $user = User::factory()->create();
        $project = Project::factory()->create(['user_id' => $user->id]);

        // Create a test file
        $file = UploadedFile::fake()->create('test.pdf', 100);
        Storage::disk('public')->put('project-files/test.pdf', $file->getContent());

        $projectFile = ProjectFile::create([
            'project_id' => $project->id,
            'filename' => 'test.pdf',
            'original_name' => 'test.pdf',
            'file_path' => 'project-files/test.pdf',
            'mime_type' => 'application/pdf',
            'file_size' => 100,
        ]);

        $response = $this->actingAs($user)
            ->get(route('project-files.download', $projectFile->id));

        $response->assertStatus(200);
        $response->assertHeader('content-disposition', 'attachment; filename=test.pdf');
    }

    public function test_assigned_freelancer_can_download_files(): void
    {
        $owner = User::factory()->create();
        $freelancer = User::factory()->create();
        $project = Project::factory()->create([
            'user_id' => $owner->id,
            'assigned_freelancer_id' => $freelancer->id,
        ]);

        // Create a test file
        $file = UploadedFile::fake()->create('test.pdf', 100);
        Storage::disk('public')->put('project-files/test.pdf', $file->getContent());

        $projectFile = ProjectFile::create([
            'project_id' => $project->id,
            'original_name' => 'test.pdf',
            'file_path' => 'project-files/test.pdf',
            'file_size' => 100,
        ]);

        $response = $this->actingAs($freelancer)
            ->get(route('project-files.download', $projectFile->id));

        $response->assertStatus(200);
        $response->assertHeader('content-disposition', 'attachment; filename=test.pdf');
    }

    public function test_unauthorized_user_cannot_download_files(): void
    {
        $owner = User::factory()->create();
        $unauthorized = User::factory()->create();
        $project = Project::factory()->create(['user_id' => $owner->id]);

        $projectFile = ProjectFile::create([
            'project_id' => $project->id,
            'original_name' => 'test.pdf',
            'file_path' => 'project-files/test.pdf',
            'file_size' => 100,
        ]);

        $response = $this->actingAs($unauthorized)
            ->get(route('project-files.download', $projectFile->id));

        $response->assertStatus(403);
    }

    public function test_unauthenticated_user_cannot_download_files(): void
    {
        $owner = User::factory()->create();
        $project = Project::factory()->create(['user_id' => $owner->id]);

        $projectFile = ProjectFile::create([
            'project_id' => $project->id,
            'original_name' => 'test.pdf',
            'file_path' => 'project-files/test.pdf',
            'file_size' => 100,
        ]);

        $response = $this->get(route('project-files.download', $projectFile->id));

        $response->assertStatus(302); // Redirect to login
    }

    public function test_nonexistent_file_returns_not_found(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->get(route('project-files.download', 999999));

        $response->assertStatus(404);
    }
}
