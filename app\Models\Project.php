<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Str;

/**
 * @property int $id
 * @property string $user_id
 * @property string $title
 * @property string|null $slug
 * @property string $description
 * @property string|null $requirements
 * @property float|null $budget_min
 * @property float|null $budget_max
 * @property string $budget_type
 * @property \Carbon\Carbon|null $deadline
 * @property string|null $category
 * @property string|null $academic_level
 * @property string $status
 * @property int $file_count
 * @property string|null $assigned_freelancer_id
 * @property float|null $accepted_bid_amount
 * @property \Carbon\Carbon|null $assigned_at
 * @property float|null $escrow_amount
 * @property string $escrow_status
 * @property int $total_milestones
 * @property int $completed_milestones
 * @property float $total_released
 * @property float $total_commission
 * @property \Carbon\Carbon|null $escrow_created_at
 * @property array|null $milestone_structure
 * @property bool $use_custom_milestones
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property-read User $user
 * @property-read User|null $assignedFreelancer
 * @property-read \Illuminate\Database\Eloquent\Collection<ProjectFile> $files
 * @property-read \Illuminate\Database\Eloquent\Collection<Bid> $bids
 * @property-read \Illuminate\Database\Eloquent\Collection<ProjectMilestone> $milestones
 */
class Project extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'title',
        'slug',
        'description',
        'requirements',
        'budget_min',
        'budget_max',
        'budget_type',
        'deadline',
        'category',
        'academic_level',
        'status',
        'file_count',
        'assigned_freelancer_id',
        'accepted_bid_amount',
        'assigned_at',
        'escrow_amount',
        'escrow_status',
        'total_milestones',
        'completed_milestones',
        'total_released',
        'total_commission',
        'escrow_created_at',
        'milestone_structure',
        'use_custom_milestones',
    ];

    protected $casts = [
        'deadline' => 'date',
        'budget_min' => 'decimal:2',
        'budget_max' => 'decimal:2',
        'accepted_bid_amount' => 'decimal:2',
        'assigned_at' => 'datetime',
        'escrow_amount' => 'decimal:2',
        'total_released' => 'decimal:2',
        'total_commission' => 'decimal:2',
        'escrow_created_at' => 'datetime',
        'milestone_structure' => 'array',
        'use_custom_milestones' => 'boolean',
    ];

    protected static function boot(): void
    {
        parent::boot();

        static::creating(function ($project) {
            if (empty($project->slug)) {
                $project->slug = static::generateUniqueSlug($project->title);
            }
        });

        static::updating(function ($project) {
            if ($project->isDirty('title') && empty($project->slug)) {
                $project->slug = static::generateUniqueSlug($project->title);
            }
        });
    }

    public static function generateUniqueSlug(string $title): string
    {
        $slug = Str::slug($title);
        $originalSlug = $slug;
        $counter = 1;

        while (static::where('slug', $slug)->exists()) {
            $slug = $originalSlug.'-'.$counter;
            $counter++;
        }

        return $slug;
    }

    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function files(): HasMany
    {
        return $this->hasMany(ProjectFile::class);
    }

    public function bids(): HasMany
    {
        return $this->hasMany(Bid::class);
    }

    public function assignedFreelancer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_freelancer_id');
    }

    public function acceptedBid(): HasOne
    {
        return $this->hasOne(Bid::class)->where('status', 'accepted');
    }

    public function milestones(): HasMany
    {
        return $this->hasMany(ProjectMilestone::class)->orderBy('order_index');
    }

    public function escrowTransactions(): HasMany
    {
        return $this->hasMany(EscrowTransaction::class);
    }

    public function platformCommissions(): HasMany
    {
        return $this->hasMany(PlatformCommission::class);
    }

    public function getBudgetDisplayAttribute(): string
    {
        if ($this->budget_type === 'negotiable') {
            return 'Negotiable';
        }

        if ($this->budget_min && $this->budget_max) {
            return '₵'.number_format((float) $this->budget_min, 2).' - ₵'.number_format((float) $this->budget_max, 2);
        }

        if ($this->budget_min) {
            return '₵'.number_format((float) $this->budget_min, 2);
        }

        return 'Not specified';
    }

    /**
     * Get the milestone structure for this project
     */
    public function getMilestoneStructure(): array
    {
        if ($this->use_custom_milestones && $this->milestone_structure) {
            return $this->milestone_structure;
        }

        // Default chapter-based structure
        $milestones = [];
        for ($i = 1; $i <= $this->total_milestones; $i++) {
            $milestones[] = [
                'title' => "Chapter {$i}",
                'description' => "Complete Chapter {$i} of the project",
                'payment_percentage' => 100 / $this->total_milestones,
            ];
        }

        return $milestones;
    }

    /**
     * Set custom milestone structure
     */
    public function setCustomMilestones(array $milestones): void
    {
        // Validate that percentages add up to 100
        $totalPercentage = array_sum(array_column($milestones, 'payment_percentage'));
        if (abs($totalPercentage - 100) > 0.01) {
            throw new \InvalidArgumentException('Milestone payment percentages must total 100%');
        }

        $this->update([
            'milestone_structure' => $milestones,
            'use_custom_milestones' => true,
            'total_milestones' => count($milestones),
        ]);
    }
}
