<?php

namespace Database\Seeders;

use App\Models\PlatformSetting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PlatformSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Set initial commission rate to 30%
        PlatformSetting::setCommissionRate(30.0);

        // Add other default settings as needed
        PlatformSetting::set(
            'site_name',
            'ThesyLink',
            'string',
            'Platform name displayed throughout the site'
        );

        PlatformSetting::set(
            'maintenance_mode',
            false,
            'boolean',
            'Whether the platform is in maintenance mode'
        );
    }
}
