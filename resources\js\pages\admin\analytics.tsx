import ActivityFeed from '@/components/admin/analytics/activity-feed';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/app-layout';
import { PageProps } from '@/types';
import { Head, router } from '@inertiajs/react';
import { BarChart3, Briefcase, DollarSign, Filter, Target, Users } from 'lucide-react';
import { useState } from 'react';

interface UserStats {
    total_users: number;
    new_users_this_month: number;
    active_users: number;
    verified_users: number;
    users_by_role: Record<string, number>;
}

interface ProjectStats {
    total_projects: number;
    open_projects: number;
    in_progress_projects: number;
    completed_projects: number;
    projects_by_category: Record<string, number>;
    projects_by_academic_level: Record<string, number>;
}

interface FinancialStats {
    total_wallet_balance: number;
    total_transactions: number;
    total_deposits: number;
    total_withdrawals: number;
    pending_withdrawals: number;
    total_commission_earned: number;
    pending_commission: number;
    commission_withdrawn: number;
    available_commission: number;
    revenue_by_month: Array<{
        month: string;
        deposits: number;
        withdrawals: number;
    }>;
    commission_by_month: Array<{
        month: string;
        total_commission: number;
    }>;
}

interface BidStats {
    total_bids: number;
    pending_bids: number;
    accepted_bids: number;
    rejected_bids: number;
    average_bid_amount: number;
}

interface HealthMetrics {
    project_completion_rate: number;
    user_verification_rate: number;
    bid_acceptance_rate: number;
    average_project_value?: number;
}

interface RecentActivity {
    recent_users: Array<{
        id: string;
        name: string;
        email: string;
        created_at: string;
        role: string;
    }>;
    recent_projects: Array<{
        id: string;
        title: string;
        user_id: string;
        status: string;
        created_at: string;
        user: {
            id: string;
            name: string;
        };
    }>;
    recent_transactions?: Array<{
        id: string;
        user_id: string;
        type: string;
        amount: number;
        status: string;
        created_at: string;
        user: {
            id: string;
            name: string;
        };
    }>;
    recent_commissions?: Array<{
        id: string;
        project_id: string;
        milestone_id?: string;
        amount: number;
        status: string;
        collected_at: string;
        project: {
            id: string;
            title: string;
        };
        milestone?: {
            id: string;
            title: string;
        };
    }>;
}

interface AdminAnalyticsProps extends PageProps {
    userStats: UserStats;
    projectStats: ProjectStats;
    financialStats: FinancialStats;
    bidStats: BidStats;
    recentActivity: RecentActivity;
    growthMetrics: any;
    healthMetrics: HealthMetrics;
    permissions: {
        can_view_financial_data: boolean;
    };
    filters: {
        start_date: string;
        end_date: string;
    };
}

export default function AdminAnalytics({
    auth,
    userStats,
    projectStats,
    financialStats,
    bidStats,
    recentActivity,
    growthMetrics,
    healthMetrics,
    permissions,
    filters,
}: AdminAnalyticsProps) {
    const [startDate, setStartDate] = useState(filters.start_date);
    const [endDate, setEndDate] = useState(filters.end_date);

    const handleApplyFilter = () => {
        router.get('/admin/analytics', {
            start_date: startDate,
            end_date: endDate,
        });
    };

    return (
        <AppLayout>
            <Head title="Analytics Dashboard" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center gap-3">
                    <BarChart3 className="h-8 w-8 text-primary" />
                    <div>
                        <h1 className="text-3xl font-bold text-foreground">Analytics Dashboard</h1>
                        <p className="text-muted-foreground">Platform insights and performance metrics</p>
                    </div>
                </div>

                {/* Date Range Filter */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-lg">
                            <Filter className="h-5 w-5" />
                            Date Range Filter
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-col gap-4 md:flex-row md:items-end">
                            <div className="flex-1">
                                <Label htmlFor="start-date">Start Date</Label>
                                <Input
                                    id="start-date"
                                    type="date"
                                    value={startDate}
                                    onChange={(e) => setStartDate(e.target.value)}
                                    className="mt-1"
                                />
                            </div>
                            <div className="flex-1">
                                <Label htmlFor="end-date">End Date</Label>
                                <Input id="end-date" type="date" value={endDate} onChange={(e) => setEndDate(e.target.value)} className="mt-1" />
                            </div>
                            <Button onClick={handleApplyFilter} className="bg-primary hover:bg-primary/90">
                                Apply Filter
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Main Stats Cards */}
                <div className="grid gap-6 md:grid-cols-3">
                    {/* Total Users */}
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Users</p>
                                    <p className="text-3xl font-bold text-primary">{userStats.total_users}</p>
                                    <p className="text-sm font-medium text-accent">+{userStats.new_users_this_month} this month</p>
                                </div>
                                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                                    <Users className="h-6 w-6 text-primary" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Total Projects */}
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Projects</p>
                                    <p className="text-3xl font-bold text-accent">{projectStats.total_projects}</p>
                                    <p className="text-sm font-medium text-accent">{projectStats.open_projects} open</p>
                                </div>
                                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-accent/10">
                                    <Briefcase className="h-6 w-6 text-accent" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Total Bids */}
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Bids</p>
                                    <p className="text-3xl font-bold text-destructive">{bidStats.total_bids}</p>
                                    <p className="text-sm font-medium text-destructive">{bidStats.accepted_bids} accepted</p>
                                </div>
                                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-destructive/10">
                                    <Target className="h-6 w-6 text-destructive" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Health Metrics */}
                <div className="grid gap-6 md:grid-cols-3">
                    <Card>
                        <CardContent className="p-6">
                            <h3 className="mb-2 text-lg font-semibold text-foreground">Project Completion Rate</h3>
                            <p className="text-3xl font-bold text-accent">{healthMetrics.project_completion_rate.toFixed(1)}%</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <h3 className="mb-2 text-lg font-semibold text-foreground">User Verification Rate</h3>
                            <p className="text-3xl font-bold text-primary">{healthMetrics.user_verification_rate.toFixed(1)}%</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <h3 className="mb-2 text-lg font-semibold text-foreground">Bid Acceptance Rate</h3>
                            <p className="text-3xl font-bold text-secondary">{healthMetrics.bid_acceptance_rate.toFixed(1)}%</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Bottom Stats */}
                <div className="grid gap-6 md:grid-cols-2">
                    {/* User Statistics */}
                    <Card>
                        <CardHeader>
                            <CardTitle>User Statistics</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="rounded-lg bg-accent/10 p-4 text-center">
                                    <p className="text-3xl font-bold text-accent">{userStats.active_users}</p>
                                    <p className="text-sm text-muted-foreground">Active Users</p>
                                </div>
                                <div className="rounded-lg bg-primary/10 p-4 text-center">
                                    <p className="text-3xl font-bold text-primary">{userStats.verified_users}</p>
                                    <p className="text-sm text-muted-foreground">Verified Users</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Project Statistics */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Project Statistics</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-3">
                                <div className="rounded-lg bg-yellow-50 p-4 text-center dark:bg-yellow-900/20">
                                    <p className="text-2xl font-bold text-yellow-600">{projectStats.open_projects}</p>
                                    <p className="text-sm text-muted-foreground">Open</p>
                                </div>
                                <div className="rounded-lg bg-blue-50 p-4 text-center dark:bg-blue-900/20">
                                    <p className="text-2xl font-bold text-blue-600">{projectStats.in_progress_projects}</p>
                                    <p className="text-sm text-muted-foreground">In Progress</p>
                                </div>
                                <div className="rounded-lg bg-accent/10 p-4 text-center">
                                    <p className="text-2xl font-bold text-accent">{projectStats.completed_projects}</p>
                                    <p className="text-sm text-muted-foreground">Completed</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Commission Data - Only for Super Admins */}
                {permissions.can_view_financial_data && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <DollarSign className="h-5 w-5 text-primary" />
                                Commission Overview
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-6 md:grid-cols-4">
                                <div className="rounded-lg bg-primary/10 p-4 text-center">
                                    <p className="text-2xl font-bold text-primary">${financialStats.total_commission_earned?.toFixed(2) || '0.00'}</p>
                                    <p className="text-sm text-muted-foreground">Total Commission</p>
                                </div>
                                <div className="rounded-lg bg-secondary/10 p-4 text-center">
                                    <p className="text-2xl font-bold text-secondary">${financialStats.pending_commission?.toFixed(2) || '0.00'}</p>
                                    <p className="text-sm text-muted-foreground">Pending Commission</p>
                                </div>
                                <div className="rounded-lg bg-accent/10 p-4 text-center">
                                    <p className="text-2xl font-bold text-accent">${financialStats.available_commission?.toFixed(2) || '0.00'}</p>
                                    <p className="text-sm text-muted-foreground">Available Commission</p>
                                </div>
                                <div className="rounded-lg bg-muted p-4 text-center">
                                    <p className="text-2xl font-bold text-muted-foreground">
                                        ${financialStats.commission_withdrawn?.toFixed(2) || '0.00'}
                                    </p>
                                    <p className="text-sm text-muted-foreground">Withdrawn</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Recent Activities */}
                <ActivityFeed
                    recentActivity={recentActivity}
                    permissions={permissions}
                    formatCurrency={(amount: number) =>
                        new Intl.NumberFormat('en-US', {
                            style: 'currency',
                            currency: 'USD',
                        }).format(amount)
                    }
                />
            </div>
        </AppLayout>
    );
}
