import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { Link } from '@inertiajs/react';
import axios from 'axios';
import { Bell, Search } from 'lucide-react';
import { useEffect, useState } from 'react';

interface Notification {
    id: number;
    title: string;
    message: string;
    time: string;
    read: boolean;
    type: 'info' | 'success' | 'warning' | 'error';
    action_url?: string;
}

export function NotificationsDropdown() {
    const [notifications, setNotifications] = useState<Notification[]>([]);
    const [loading, setLoading] = useState(false);

    const unreadCount = notifications.filter((n) => !n.read).length;

    // Mark notification as read
    const markAsRead = async (notificationId: number) => {
        try {
            const response = await axios.patch(`/notifications/${notificationId}/read`);

            if (response.status === 200) {
                // Update local state
                setNotifications((prev) => prev.map((n) => (n.id === notificationId ? { ...n, read: true } : n)));
            }
        } catch (error) {
            console.error('Failed to mark notification as read:', error);
        }
    };

    // Mark all notifications as read
    const markAllAsRead = async () => {
        try {
            const response = await axios.post('/notifications/mark-all-read');

            if (response.status === 200) {
                // Update local state
                setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
            }
        } catch (error) {
            console.error('Failed to mark all notifications as read:', error);
        }
    };

    // Handle notification click
    const handleNotificationClick = (notification: Notification) => {
        if (!notification.read) {
            markAsRead(notification.id);
        }

        // Navigate to the action URL if it exists
        if (notification.action_url && notification.action_url !== '#') {
            window.location.href = notification.action_url;
        }
    };

    const fetchNotifications = async () => {
        setLoading(true);
        try {
            const response = await axios.get('/notifications/dropdown');

            if (response.status === 200 && response.data.success) {
                setNotifications(response.data.notifications);
            } else {
                console.error('Failed to fetch notifications:', response.data.message);
                setNotifications([]);
            }
        } catch (error) {
            console.error('Failed to fetch notifications:', error);
            setNotifications([]);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchNotifications();

        // Set up polling every 30 seconds to check for new notifications
        const interval = setInterval(fetchNotifications, 30000);

        return () => clearInterval(interval);
    }, []);

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="group relative h-9 w-9 cursor-pointer" onClick={fetchNotifications}>
                    <Bell className="size-5 opacity-70 group-hover:opacity-100" />
                    {unreadCount > 0 && (
                        <Badge className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-destructive p-0 text-xs text-destructive-foreground">
                            {unreadCount > 99 ? '99+' : unreadCount}
                        </Badge>
                    )}
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-96 border border-border bg-background p-0 shadow-lg" align="end" side="bottom" sideOffset={8}>
                {/* Header */}
                <div className="flex items-center justify-between border-b border-border p-4">
                    <h3 className="text-lg font-semibold text-foreground">Notifications</h3>
                    <div className="flex items-center gap-2">
                        {unreadCount > 0 && (
                            <>
                                <Badge variant="secondary" className="text-xs">
                                    {unreadCount} new
                                </Badge>
                                <Button variant="ghost" size="sm" onClick={markAllAsRead} className="text-xs">
                                    Mark all read
                                </Button>
                            </>
                        )}
                    </div>
                </div>

                {/* Notifications List */}
                <ScrollArea className="h-96">
                    {loading ? (
                        <div className="flex h-64 flex-col items-center justify-center p-6 text-center">
                            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
                            <p className="mt-4 text-sm text-muted-foreground">Loading notifications...</p>
                        </div>
                    ) : notifications.length > 0 ? (
                        <div className="divide-y divide-border">
                            {notifications.map((notification) => (
                                <div
                                    key={notification.id}
                                    className={cn(
                                        'cursor-pointer p-4 transition-colors hover:bg-muted/50',
                                        !notification.read && 'border-l-2 border-l-primary bg-primary/5',
                                    )}
                                    onClick={() => handleNotificationClick(notification)}
                                >
                                    <div className="flex items-start gap-3">
                                        <div
                                            className={cn(
                                                'mt-2 h-2 w-2 flex-shrink-0 rounded-full',
                                                notification.type === 'success' && 'bg-accent',
                                                notification.type === 'info' && 'bg-primary',
                                                notification.type === 'warning' && 'bg-[#F9C45C]',
                                                notification.type === 'error' && 'bg-destructive',
                                            )}
                                        />
                                        <div className="min-w-0 flex-1">
                                            <p className="truncate text-sm font-medium text-foreground">{notification.title}</p>
                                            <p className="mt-1 line-clamp-2 text-sm text-muted-foreground">{notification.message}</p>
                                            <p className="mt-2 text-xs text-muted-foreground">{notification.time}</p>
                                        </div>
                                        {!notification.read && <div className="mt-2 h-2 w-2 flex-shrink-0 rounded-full bg-primary" />}
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="flex h-64 flex-col items-center justify-center p-6 text-center">
                            <Bell className="mb-4 h-12 w-12 text-muted-foreground/50" />
                            <h4 className="mb-2 text-lg font-medium text-foreground">No notifications</h4>
                            <p className="mb-4 text-sm text-muted-foreground">
                                You don't have any notifications as of this moment.
                                <br />
                                Would you like to search for a project instead?
                            </p>
                            <Button asChild variant="outline" size="sm">
                                <Link href="/browse">
                                    <Search className="mr-2 h-4 w-4" />
                                    Search Project
                                </Link>
                            </Button>
                        </div>
                    )}
                </ScrollArea>

                {/* Footer */}
                {notifications.length > 0 && (
                    <div className="border-t border-border p-3">
                        <Button asChild variant="ghost" className="w-full text-sm">
                            <Link href="/notifications">View All Notifications</Link>
                        </Button>
                    </div>
                )}
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
