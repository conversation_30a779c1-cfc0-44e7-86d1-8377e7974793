import ActionDialogs from '@/components/admin/user-management/action-dialogs';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { PageProps } from '@/types';
import { Head, Link, router, useForm } from '@inertiajs/react';
import {
    CheckCircle,
    Edit,
    Eye,
    Filter,
    MoreHorizontal,
    Search,
    Shield,
    ShieldCheck,
    ShieldOff,
    Trash2,
    User,
    UserMinus,
    UserPlus,
    UserX,
    Wallet,
    XCircle,
} from 'lucide-react';
import { useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
    email_verified_at: string | null;
    role: string;
    status: 'active' | 'inactive' | 'suspended';
    wallet_balance: number;
    created_at: string;
    projects_count: number;
    bids_count: number;
    last_login: string | null;
}

interface UserManagementProps extends PageProps {
    users: {
        data: User[];
        total: number;
        per_page: number;
        current_page: number;
        last_page: number;
    };
    filters: {
        search?: string;
        role?: string;
        status?: string;
        verified?: string;
    };
    stats: {
        total: number;
        active: number;
        suspended: number;
        blocked: number;
        verified: number;
    };
}

export default function UserManagement({ auth, users, filters, stats }: UserManagementProps) {
    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [roleFilter, setRoleFilter] = useState(filters.role || 'all');
    const [statusFilter, setStatusFilter] = useState(filters.status || 'all');
    const [verificationFilter, setVerificationFilter] = useState(filters.verified || 'all');

    // Action dialog state
    const [selectedUser, setSelectedUser] = useState<any>(null);
    const [actionType, setActionType] = useState<'status' | 'verification' | 'wallet' | 'delete' | null>(null);

    const actionForm = useForm({
        status: '',
        reason: '',
        is_verified: false,
        amount: '',
        type: 'subtract',
        wallet_reason: '',
    });

    // Confirmation dialog states
    const [deleteDialog, setDeleteDialog] = useState<{ isOpen: boolean; userId: number | null }>({
        isOpen: false,
        userId: null,
    });
    const [suspendDialog, setSuspendDialog] = useState<{ isOpen: boolean; userId: number | null }>({
        isOpen: false,
        userId: null,
    });
    const [blockDialog, setBlockDialog] = useState<{ isOpen: boolean; userId: number | null }>({
        isOpen: false,
        userId: null,
    });
    const [removeVerificationDialog, setRemoveVerificationDialog] = useState<{ isOpen: boolean; userId: number | null }>({
        isOpen: false,
        userId: null,
    });
    const [loading, setLoading] = useState(false);

    const handleSearch = () => {
        router.get(
            '/admin/users',
            {
                search: searchQuery,
                role: roleFilter === 'all' ? '' : roleFilter,
                status: statusFilter === 'all' ? '' : statusFilter,
                verified: verificationFilter === 'all' ? '' : verificationFilter,
            },
            { preserveState: true },
        );
    };

    const handleApplyFilters = () => {
        router.get(
            '/admin/users',
            {
                search: searchQuery,
                role: roleFilter === 'all' ? '' : roleFilter,
                status: statusFilter === 'all' ? '' : statusFilter,
                verified: verificationFilter === 'all' ? '' : verificationFilter,
            },
            { preserveState: true },
        );
    };

    const handleDelete = (userId: number) => {
        setDeleteDialog({ isOpen: true, userId });
    };

    const confirmDelete = () => {
        if (deleteDialog.userId) {
            setLoading(true);
            router.delete(`/admin/users/${deleteDialog.userId}`, {
                onFinish: () => {
                    setLoading(false);
                    setDeleteDialog({ isOpen: false, userId: null });
                },
            });
        }
    };

    const handleSuspendUser = (userId: number) => {
        setSuspendDialog({ isOpen: true, userId });
    };

    const confirmSuspend = () => {
        if (suspendDialog.userId) {
            setLoading(true);
            router.patch(
                `/admin/users/${suspendDialog.userId}/suspend`,
                {},
                {
                    onFinish: () => {
                        setLoading(false);
                        setSuspendDialog({ isOpen: false, userId: null });
                    },
                    onError: (errors) => {
                        console.error('Error suspending user:', errors);
                        alert('Failed to suspend user. Please try again.');
                    },
                },
            );
        }
    };

    const handleBlockUser = (userId: number) => {
        setBlockDialog({ isOpen: true, userId });
    };

    const confirmBlock = () => {
        if (blockDialog.userId) {
            setLoading(true);
            router.patch(
                `/admin/users/${blockDialog.userId}/block`,
                {},
                {
                    onFinish: () => {
                        setLoading(false);
                        setBlockDialog({ isOpen: false, userId: null });
                    },
                    onError: (errors) => {
                        console.error('Error blocking user:', errors);
                        alert('Failed to block user. Please try again.');
                    },
                },
            );
        }
    };

    const handleRemoveVerification = (userId: number) => {
        setRemoveVerificationDialog({ isOpen: true, userId });
    };

    const confirmRemoveVerification = () => {
        if (removeVerificationDialog.userId) {
            setLoading(true);
            router.patch(
                `/admin/users/${removeVerificationDialog.userId}/remove-verification`,
                {},
                {
                    onFinish: () => {
                        setLoading(false);
                        setRemoveVerificationDialog({ isOpen: false, userId: null });
                    },
                    onError: (errors) => {
                        console.error('Error removing verification:', errors);
                        alert('Failed to remove verification. Please try again.');
                    },
                },
            );
        }
    };

    const handleAdjustWallet = (user: User) => {
        setSelectedUser({
            id: user.id.toString(),
            name: user.name,
            email: user.email,
            role: user.role as 'user' | 'admin' | 'super_admin',
            status: user.status as 'active' | 'suspended' | 'blocked',
            is_verified: !!user.email_verified_at,
            wallet_balance: user.wallet_balance,
            created_at: user.created_at,
            projects_count: user.projects_count,
            bids_count: user.bids_count,
        });
        setActionType('wallet');
    };

    const handleActionExecute = () => {
        if (!actionType || !selectedUser) return;

        const routes = {
            status: `/admin/users/${selectedUser.id}/status`,
            verification: `/admin/users/${selectedUser.id}/verification`,
            wallet: `/admin/users/${selectedUser.id}/adjust-wallet`,
            delete: `/admin/users/${selectedUser.id}`,
        };

        const method = actionType === 'delete' ? 'delete' : 'patch';
        const route = routes[actionType];

        if (method === 'delete') {
            router.delete(route, {
                onSuccess: () => {
                    router.reload();
                },
                onFinish: () => {
                    setSelectedUser(null);
                    setActionType(null);
                },
            });
        } else {
            router.patch(route, actionForm.data, {
                onSuccess: () => {
                    router.reload();
                },
                onFinish: () => {
                    setSelectedUser(null);
                    setActionType(null);
                },
            });
        }
    };

    const handleActionClose = () => {
        setSelectedUser(null);
        setActionType(null);
        actionForm.reset();
    };

    const handleCreateUser = () => {
        router.get('/admin/users/create');
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'active':
                return 'bg-green-100 text-green-800';
            case 'inactive':
                return 'bg-gray-100 text-gray-800';
            case 'suspended':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getRoleColor = (role: string) => {
        switch (role) {
            case 'admin':
                return 'bg-purple-100 text-purple-800';
            case 'moderator':
                return 'bg-blue-100 text-blue-800';
            case 'user':
                return 'bg-green-100 text-green-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString();
    };

    return (
        <AppLayout>
            <Head title="User Management" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <Shield className="h-8 w-8 text-red-500" />
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">User Management</h1>
                            <p className="text-gray-600 dark:text-gray-400">
                                Manage all users on the platform - view, edit, suspend, or verify user accounts
                            </p>
                        </div>
                    </div>
                    <Button onClick={handleCreateUser} className="bg-blue-600 hover:bg-blue-700">
                        <UserPlus className="mr-2 h-4 w-4" />
                        Create User
                    </Button>
                </div>

                {/* Statistics Cards */}
                <div className="grid grid-cols-2 gap-4 md:grid-cols-5">
                    <Card>
                        <CardContent className="flex items-center gap-3 p-6">
                            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100">
                                <User className="h-6 w-6 text-blue-600" />
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-600">Total Users</p>
                                <p className="text-2xl font-bold text-gray-900">{stats?.total || users.total}</p>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="flex items-center gap-3 p-6">
                            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100">
                                <CheckCircle className="h-6 w-6 text-green-600" />
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-600">Active</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {stats?.active || users.data.filter((u) => u.status === 'active').length}
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="flex items-center gap-3 p-6">
                            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-100">
                                <UserMinus className="h-6 w-6 text-orange-600" />
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-600">Suspended</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {stats?.suspended || users.data.filter((u) => u.status === 'suspended').length}
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="flex items-center gap-3 p-6">
                            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-red-100">
                                <XCircle className="h-6 w-6 text-red-600" />
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-600">Blocked</p>
                                <p className="text-2xl font-bold text-gray-900">{stats?.blocked || 0}</p>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="flex items-center gap-3 p-6">
                            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100">
                                <ShieldCheck className="h-6 w-6 text-blue-600" />
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-600">Verified</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {stats?.verified || users.data.filter((u) => u.email_verified_at).length}
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters & Search */}
                <Card>
                    <CardContent className="p-6">
                        <div className="mb-4 flex items-center gap-3">
                            <Filter className="h-5 w-5 text-gray-600" />
                            <h3 className="text-lg font-semibold text-gray-900">Filters & Search</h3>
                        </div>
                        <div className="flex flex-wrap items-end gap-4">
                            <div className="min-w-[200px] flex-1">
                                <label className="mb-1 block text-sm font-medium text-gray-700">Search</label>
                                <div className="relative">
                                    <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                    <Input
                                        placeholder="Name, email, phone..."
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        onKeyDown={(e) => e.key === 'Enter' && handleApplyFilters()}
                                        className="pl-10"
                                    />
                                </div>
                            </div>

                            <div className="min-w-[150px]">
                                <label className="mb-1 block text-sm font-medium text-gray-700">Role</label>
                                <Select value={roleFilter} onValueChange={setRoleFilter}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="All Roles" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Roles</SelectItem>
                                        <SelectItem value="user">User</SelectItem>
                                        <SelectItem value="admin">Admin</SelectItem>
                                        <SelectItem value="super_admin">Super Admin</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="min-w-[150px]">
                                <label className="mb-1 block text-sm font-medium text-gray-700">Status</label>
                                <Select value={statusFilter} onValueChange={setStatusFilter}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="All Status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Status</SelectItem>
                                        <SelectItem value="active">Active</SelectItem>
                                        <SelectItem value="inactive">Inactive</SelectItem>
                                        <SelectItem value="suspended">Suspended</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="min-w-[150px]">
                                <label className="mb-1 block text-sm font-medium text-gray-700">Verification</label>
                                <Select value={verificationFilter} onValueChange={setVerificationFilter}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="All" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All</SelectItem>
                                        <SelectItem value="verified">Verified</SelectItem>
                                        <SelectItem value="unverified">Unverified</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            <Button onClick={handleApplyFilters} className="bg-blue-600 hover:bg-blue-700">
                                Apply Filters
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Users Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>Users ({users.total})</CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-gray-50 dark:bg-gray-800">
                                        <TableHead className="font-semibold text-gray-700 dark:text-gray-200">USER</TableHead>
                                        <TableHead className="font-semibold text-gray-700 dark:text-gray-200">ROLE & STATUS</TableHead>
                                        <TableHead className="font-semibold text-gray-700 dark:text-gray-200">WALLET</TableHead>
                                        <TableHead className="font-semibold text-gray-700 dark:text-gray-200">ACTIVITY</TableHead>
                                        <TableHead className="font-semibold text-gray-700 dark:text-gray-200">ACTIONS</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {users.data.map((user) => (
                                        <TableRow key={user.id} className="border-b border-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800">
                                            <TableCell className="py-4">
                                                <div className="flex items-center gap-3">
                                                    <Avatar className="h-10 w-10">
                                                        <AvatarFallback className="bg-blue-100 font-semibold text-blue-600">
                                                            {user.name.charAt(0).toUpperCase()}
                                                        </AvatarFallback>
                                                    </Avatar>
                                                    <div>
                                                        <div className="flex items-center gap-2">
                                                            <span className="font-medium text-gray-900 dark:text-gray-100">{user.name}</span>
                                                            {user.email_verified_at && <CheckCircle className="h-4 w-4 text-green-500" />}
                                                        </div>
                                                        <div className="text-sm text-gray-500">{user.email}</div>
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell className="py-4">
                                                <div className="flex flex-col gap-1">
                                                    <Badge className={getRoleColor(user.role)} variant="secondary">
                                                        {user.role.toUpperCase()}
                                                    </Badge>
                                                    <Badge className={getStatusColor(user.status)} variant="secondary">
                                                        {user.status.toUpperCase()}
                                                    </Badge>
                                                </div>
                                            </TableCell>
                                            <TableCell className="py-4">
                                                <div className="font-medium text-gray-900 dark:text-gray-100">
                                                    ₵{Number(user.wallet_balance || 0).toFixed(2)}
                                                </div>
                                            </TableCell>
                                            <TableCell className="py-4">
                                                <div className="text-sm">
                                                    <div>
                                                        {user.projects_count} project{user.projects_count !== 1 ? 's' : ''}
                                                    </div>
                                                    <div className="text-gray-500">
                                                        {user.bids_count} bid{user.bids_count !== 1 ? 's' : ''}
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell className="py-4">
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" className="h-8 w-8 p-0">
                                                            <span className="sr-only">Open menu</span>
                                                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end" className="w-48">
                                                        <div className="px-2 py-1.5 text-sm font-semibold text-gray-700 dark:text-gray-200">
                                                            User Actions
                                                        </div>
                                                        <DropdownMenuItem asChild>
                                                            <Link href={`/admin/users/${user.id}`} className="flex items-center">
                                                                <Eye className="mr-2 h-4 w-4" />
                                                                View Details
                                                            </Link>
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem asChild>
                                                            <Link href={`/admin/users/${user.id}/edit`} className="flex items-center">
                                                                <Edit className="mr-2 h-4 w-4" />
                                                                Edit User
                                                            </Link>
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem
                                                            onClick={() => handleSuspendUser(user.id)}
                                                            className="flex cursor-pointer items-center text-orange-600"
                                                        >
                                                            <UserMinus className="mr-2 h-4 w-4" />
                                                            Suspend
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem
                                                            onClick={() => handleBlockUser(user.id)}
                                                            className="flex cursor-pointer items-center text-red-600"
                                                        >
                                                            <UserX className="mr-2 h-4 w-4" />
                                                            Block
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem
                                                            onClick={() => handleRemoveVerification(user.id)}
                                                            className="flex cursor-pointer items-center text-blue-600"
                                                        >
                                                            <ShieldOff className="mr-2 h-4 w-4" />
                                                            Remove Verification
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem
                                                            onClick={() => handleAdjustWallet(user)}
                                                            className="flex cursor-pointer items-center text-green-600"
                                                        >
                                                            <Wallet className="mr-2 h-4 w-4" />
                                                            Adjust Wallet
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Pagination */}
                        {users.last_page > 1 && (
                            <div className="flex items-center justify-between border-t border-gray-200 px-6 py-4">
                                <div className="text-sm text-gray-500">
                                    Showing {(users.current_page - 1) * users.per_page + 1} to{' '}
                                    {Math.min(users.current_page * users.per_page, users.total)} of {users.total} results
                                </div>
                                <div className="flex items-center gap-2">
                                    {Array.from({ length: users.last_page }, (_, i) => i + 1).map((page) => (
                                        <Button
                                            key={page}
                                            variant={page === users.current_page ? 'default' : 'outline'}
                                            size="sm"
                                            onClick={() => router.get(`/admin/users?page=${page}`)}
                                        >
                                            {page}
                                        </Button>
                                    ))}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* Confirmation Dialogs */}
            <ConfirmationDialog
                isOpen={deleteDialog.isOpen}
                onClose={() => setDeleteDialog({ isOpen: false, userId: null })}
                onConfirm={confirmDelete}
                title="Delete User"
                description="Are you sure you want to delete this user? This action cannot be undone and will permanently remove all user data."
                type="danger"
                confirmText="Delete User"
                icon={<Trash2 className="h-6 w-6" />}
                loading={loading}
            />

            <ConfirmationDialog
                isOpen={suspendDialog.isOpen}
                onClose={() => setSuspendDialog({ isOpen: false, userId: null })}
                onConfirm={confirmSuspend}
                title="Suspend User"
                description="Are you sure you want to suspend this user? They will not be able to access their account until you reactivate them."
                type="warning"
                confirmText="Suspend User"
                icon={<UserMinus className="h-6 w-6" />}
                loading={loading}
            />

            <ConfirmationDialog
                isOpen={blockDialog.isOpen}
                onClose={() => setBlockDialog({ isOpen: false, userId: null })}
                onConfirm={confirmBlock}
                title="Block User"
                description="Are you sure you want to block this user? This will prevent them from accessing the platform completely."
                type="danger"
                confirmText="Block User"
                icon={<UserX className="h-6 w-6" />}
                loading={loading}
            />

            <ConfirmationDialog
                isOpen={removeVerificationDialog.isOpen}
                onClose={() => setRemoveVerificationDialog({ isOpen: false, userId: null })}
                onConfirm={confirmRemoveVerification}
                title="Remove Verification"
                description="Are you sure you want to remove verification from this user? They will need to verify their account again."
                type="warning"
                confirmText="Remove Verification"
                icon={<ShieldOff className="h-6 w-6" />}
                loading={loading}
            />

            <ActionDialogs
                selectedUser={selectedUser}
                actionType={actionType}
                actionForm={actionForm}
                onClose={handleActionClose}
                onExecute={handleActionExecute}
            />
        </AppLayout>
    );
}
