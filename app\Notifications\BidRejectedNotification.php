<?php

namespace App\Notifications;

use App\Models\Bid;
use App\Models\Project;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class BidRejectedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public Bid $bid,
        public Project $project,
        public User $client
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database']; // Only database for rejections, not email to avoid spam
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Proposal Update - '.$this->project->title)
            ->greeting('Hello '.$notifiable->name.'!')
            ->line('Thank you for your interest in "'.$this->project->title.'".')
            ->line('After careful consideration, the client has decided to move forward with a different proposal.')
            ->line('**Project:** '.$this->project->title)
            ->line('**Your Proposed Amount:** ₵'.number_format((float) $this->bid->amount, 2))
            ->line('Don\'t be discouraged! There are many other exciting projects available on Thesylink.')
            ->action('Browse More Projects', url('/browse'))
            ->line('Keep improving your proposals and you\'ll find the perfect match.')
            ->line('Thank you for being part of the Thesylink community!')
            ->salutation('Best regards,  
The Thesylink Team');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'bid_rejected',
            'bid_id' => $this->bid->id,
            'project_id' => $this->project->id,
            'project_title' => $this->project->title,
            'project_slug' => $this->project->slug,
            'client_id' => $this->client->id,
            'client_name' => $this->client->name,
            'bid_amount' => $this->bid->amount,
            'action_url' => '/browse',
            'message' => 'Your proposal for "'.$this->project->title.'" was not selected.',
            'title' => 'Proposal Update',
        ];
    }
}
