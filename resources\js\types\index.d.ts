import { LucideIcon } from 'lucide-react';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface PageProps {
    auth: Auth;
    ziggy: Config & { location: string };
    [key: string]: any;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon | null;
    isActive?: boolean;
}

export interface SharedData {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
    [key: string]: unknown;
}

export interface User {
    id: string;
    name: string;
    email: string;
    avatar?: string;
    email_verified_at: string | null;
    role: 'user' | 'admin' | 'super_admin';
    status: 'active' | 'suspended' | 'blocked';
    wallet_balance: number;
    bio?: string;
    skills?: string[];
    education?: string;
    is_verified: boolean;
    phone?: string;
    location?: string;
    last_activity?: string;
    created_at: string;
    updated_at: string;
    [key: string]: unknown; // This allows for additional properties...
}
