import { <PERSON><PERSON>, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import { PageProps } from '@/types';
import { Head } from '@inertiajs/react';
import axios from 'axios';
import { format } from 'date-fns';
import { MessageCircle, Search, Send } from 'lucide-react';
import React, { useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
}

interface Project {
    id: number;
    title: string;
}

interface Conversation {
    id: number;
    project: Project;
    other_user: User;
    last_message: string;
    last_message_at: string;
    unread_count: number;
}

interface Message {
    id: number;
    message: string;
    sender: User;
    created_at: string;
    is_own: boolean;
}

interface ConversationDetail {
    id: number;
    project: Project;
    other_user: User;
}

interface InboxProps extends PageProps {
    conversations: Conversation[];
}

export default function Inbox({ auth, conversations: initialConversations }: InboxProps) {
    const [conversations, setConversations] = useState<Conversation[]>(initialConversations);
    const [selectedConversation, setSelectedConversation] = useState<number | null>(null);
    const [messages, setMessages] = useState<Message[]>([]);
    const [conversationDetail, setConversationDetail] = useState<ConversationDetail | null>(null);
    const [newMessage, setNewMessage] = useState('');
    const [searchTerm, setSearchTerm] = useState('');
    const [loading, setLoading] = useState(false);

    const filteredConversations = conversations.filter(
        (conversation) =>
            conversation.other_user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            conversation.project.title.toLowerCase().includes(searchTerm.toLowerCase()),
    );

    const loadConversation = async (conversationId: number) => {
        if (selectedConversation === conversationId) return;

        setLoading(true);
        try {
            const response = await axios.get(`/api/conversations/${conversationId}`);
            setMessages(response.data.messages);
            setConversationDetail(response.data.conversation);
            setSelectedConversation(conversationId);

            // Mark conversation as read
            setConversations((prev) => prev.map((conv) => (conv.id === conversationId ? { ...conv, unread_count: 0 } : conv)));
        } catch (error) {
            console.error('Error loading conversation:', error);
        } finally {
            setLoading(false);
        }
    };

    const sendMessage = async () => {
        if (!newMessage.trim() || !selectedConversation) return;

        try {
            const response = await axios.post(`/api/conversations/${selectedConversation}/messages`, {
                message: newMessage.trim(),
            });

            setMessages((prev) => [...prev, response.data.message]);
            setNewMessage('');

            // Update conversation's last message
            setConversations((prev) =>
                prev.map((conv) =>
                    conv.id === selectedConversation
                        ? {
                              ...conv,
                              last_message: newMessage.trim(),
                              last_message_at: new Date().toISOString(),
                          }
                        : conv,
                ),
            );
        } catch (error) {
            console.error('Error sending message:', error);
        }
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    };

    const formatTimestamp = (timestamp: string) => {
        const date = new Date(timestamp);
        const now = new Date();
        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

        if (diffInHours < 24) {
            return format(date, 'HH:mm');
        } else if (diffInHours < 168) {
            // 7 days
            return format(date, 'EEE');
        } else {
            return format(date, 'MMM dd');
        }
    };

    return (
        <AppLayout>
            <Head title="Inbox" />

            <div className="mx-auto max-w-7xl p-6">
                <div className="mb-6">
                    <h1 className="text-3xl font-bold text-gray-900">Inbox</h1>
                    <p className="mt-2 text-gray-600">Manage your project conversations</p>
                </div>

                <div className="grid h-[calc(100vh-200px)] grid-cols-1 gap-6 lg:grid-cols-3">
                    {/* Conversations List */}
                    <Card className="col-span-1">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <MessageCircle className="h-5 w-5" />
                                Conversations
                            </CardTitle>
                            <div className="relative">
                                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                                <Input
                                    placeholder="Search conversations..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                        </CardHeader>
                        <CardContent className="p-0">
                            <ScrollArea className="h-[calc(100vh-320px)]">
                                {filteredConversations.length === 0 ? (
                                    <div className="p-6 text-center text-gray-500">
                                        <MessageCircle className="mx-auto mb-4 h-12 w-12 text-gray-300" />
                                        <p>No conversations found</p>
                                    </div>
                                ) : (
                                    filteredConversations.map((conversation) => (
                                        <div
                                            key={conversation.id}
                                            className={`cursor-pointer border-b p-4 transition-colors hover:bg-gray-50 ${
                                                selectedConversation === conversation.id ? 'border-blue-200 bg-blue-50' : ''
                                            }`}
                                            onClick={() => loadConversation(conversation.id)}
                                        >
                                            <div className="flex items-start gap-3">
                                                <Avatar className="h-10 w-10">
                                                    <AvatarFallback>{conversation.other_user.name.charAt(0).toUpperCase()}</AvatarFallback>
                                                </Avatar>
                                                <div className="min-w-0 flex-1">
                                                    <div className="flex items-center justify-between">
                                                        <p className="truncate font-medium text-gray-900">{conversation.other_user.name}</p>
                                                        <div className="flex items-center gap-2">
                                                            {conversation.unread_count > 0 && (
                                                                <Badge variant="destructive" className="text-xs">
                                                                    {conversation.unread_count}
                                                                </Badge>
                                                            )}
                                                            <span className="text-xs text-gray-500">
                                                                {formatTimestamp(conversation.last_message_at)}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <p className="truncate text-sm text-blue-600">{conversation.project.title}</p>
                                                    <p className="mt-1 truncate text-sm text-gray-500">
                                                        {conversation.last_message || 'No messages yet'}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    ))
                                )}
                            </ScrollArea>
                        </CardContent>
                    </Card>

                    {/* Chat Area */}
                    <Card className="col-span-2">
                        {selectedConversation && conversationDetail ? (
                            <>
                                <CardHeader>
                                    <div className="flex items-center gap-3">
                                        <Avatar className="h-10 w-10">
                                            <AvatarFallback>{conversationDetail.other_user.name.charAt(0).toUpperCase()}</AvatarFallback>
                                        </Avatar>
                                        <div>
                                            <h3 className="font-semibold">{conversationDetail.other_user.name}</h3>
                                            <p className="text-sm text-blue-600">{conversationDetail.project.title}</p>
                                        </div>
                                    </div>
                                </CardHeader>
                                <Separator />
                                <CardContent className="flex h-[calc(100vh-400px)] flex-col">
                                    {/* Messages */}
                                    <ScrollArea className="flex-1 pr-4">
                                        {loading ? (
                                            <div className="flex h-32 items-center justify-center">
                                                <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
                                            </div>
                                        ) : (
                                            <div className="space-y-4 py-4">
                                                {messages.map((message) => (
                                                    <div key={message.id} className={`flex ${message.is_own ? 'justify-end' : 'justify-start'}`}>
                                                        <div
                                                            className={`max-w-xs rounded-lg px-4 py-2 lg:max-w-md ${
                                                                message.is_own ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-900'
                                                            }`}
                                                        >
                                                            <p className="text-sm">{message.message}</p>
                                                            <p className={`mt-1 text-xs ${message.is_own ? 'text-blue-100' : 'text-gray-500'}`}>
                                                                {format(new Date(message.created_at), 'HH:mm')}
                                                            </p>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                    </ScrollArea>

                                    {/* Message Input */}
                                    <div className="flex items-end gap-2 pt-4">
                                        <div className="flex-1">
                                            <Input
                                                placeholder="Type your message..."
                                                value={newMessage}
                                                onChange={(e) => setNewMessage(e.target.value)}
                                                onKeyPress={handleKeyPress}
                                                className="resize-none"
                                            />
                                        </div>
                                        <Button onClick={sendMessage} disabled={!newMessage.trim()} size="icon">
                                            <Send className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </CardContent>
                            </>
                        ) : (
                            <CardContent className="flex h-full items-center justify-center">
                                <div className="text-center text-gray-500">
                                    <MessageCircle className="mx-auto mb-4 h-16 w-16 text-gray-300" />
                                    <h3 className="mb-2 text-lg font-medium">Select a conversation</h3>
                                    <p>Choose a conversation from the list to start chatting</p>
                                </div>
                            </CardContent>
                        )}
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
