<?php

namespace App\Http\Controllers;

use App\Models\PlatformSetting;
use Illuminate\Http\Request;
use Inertia\Inertia;

class AdminSettingsController extends Controller
{
    public function index()
    {
        $commissionRate = PlatformSetting::getCommissionRate();

        return Inertia::render('admin/settings', [
            'settings' => [
                'commission_rate' => $commissionRate,
            ],
        ]);
    }

    public function update(Request $request)
    {
        $request->validate([
            'commission_rate' => [
                'required',
                'numeric',
                'min:0',
                'max:50',
            ],
        ]);

        $commissionRate = $request->input('commission_rate');

        PlatformSetting::setCommissionRate($commissionRate);

        return redirect()->back()->with('success', 'Commission rate updated successfully.');
    }
}
