import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader } from '@/components/ui/card';
import { Link } from '@inertiajs/react';
import { Calendar, DollarSign, FileText, User } from 'lucide-react';

interface Project {
    id: number;
    title: string;
    slug?: string;
    description: string;
    budget_min?: number;
    budget_max?: number;
    budget_type: 'fixed' | 'negotiable';
    deadline?: string;
    category?: string;
    academic_level?: string;
    status: 'open' | 'in_progress' | 'completed' | 'cancelled';
    file_count: number;
    created_at: string;
    user?: {
        id: number;
        name: string;
    };
}

interface ProjectCardProps {
    project: Project;
    showAuthor?: boolean;
    variant?: 'default' | 'compact' | 'dashboard';
}

const statusColors = {
    open: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    in_progress: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    completed: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
    cancelled: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
};

const statusLabels = {
    open: 'Open',
    in_progress: 'In Progress',
    completed: 'Completed',
    cancelled: 'Cancelled',
};

export function ProjectCard({ project, showAuthor = false, variant = 'default' }: ProjectCardProps) {
    const formatBudget = () => {
        if (project.budget_type === 'negotiable') {
            return 'Negotiable';
        }

        const budgetMin = project.budget_min ? Number(project.budget_min) : null;
        const budgetMax = project.budget_max ? Number(project.budget_max) : null;

        if (budgetMin && budgetMax) {
            return `₵${budgetMin.toFixed(2)} - ₵${budgetMax.toFixed(2)}`;
        }

        return 'Not specified';
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    };

    const truncateDescription = (text: string, maxLength: number = 150) => {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength).trim() + '...';
    };

    return (
        <Card className="flex h-full flex-col transition-shadow hover:shadow-md">
            <CardHeader className="pb-3">
                <div className="flex items-start justify-between gap-2">
                    <div className="min-w-0 flex-1">
                        <h3 className="mb-2 line-clamp-2 text-lg leading-tight font-semibold">{project.title}</h3>
                        <div className="mb-2 flex flex-wrap gap-2">
                            <Badge variant="secondary" className="text-xs">
                                {project.category}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                                {project.academic_level}
                            </Badge>
                        </div>
                    </div>
                    <Badge className={`${statusColors[project.status]} text-xs whitespace-nowrap`}>{statusLabels[project.status]}</Badge>
                </div>
            </CardHeader>

            <CardContent className="flex-1 pb-3">
                <p className="mb-4 line-clamp-3 text-sm text-muted-foreground">{truncateDescription(project.description)}</p>

                <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">{formatBudget()}</span>
                    </div>

                    {project.deadline && (
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Calendar className="h-4 w-4" />
                            <span>Due: {formatDate(project.deadline)}</span>
                        </div>
                    )}

                    {project.file_count > 0 && (
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <FileText className="h-4 w-4" />
                            <span>
                                {project.file_count} file{project.file_count !== 1 ? 's' : ''}
                            </span>
                        </div>
                    )}

                    {showAuthor && project.user && (
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <User className="h-4 w-4" />
                            <span>by {project.user.name}</span>
                        </div>
                    )}
                </div>
            </CardContent>

            <CardFooter className="border-t pt-3">
                <div className="flex w-full items-center justify-between">
                    <span className="text-xs text-muted-foreground">Posted {formatDate(project.created_at)}</span>
                    <div className="flex gap-2">
                        <Button variant="outline" size="sm" asChild>
                            <Link href={`/projects/${project.slug || project.id}`}>View Details</Link>
                        </Button>
                        {variant === 'dashboard' && (
                            <Button variant="outline" size="sm" asChild>
                                <Link href={`/projects/${project.slug || project.id}/edit`}>Edit</Link>
                            </Button>
                        )}
                        {showAuthor && project.status === 'open' && (
                            <Button size="sm" asChild>
                                <Link href={`/projects/${project.slug || project.id}#submit-proposal`}>Apply Now</Link>
                            </Button>
                        )}
                    </div>
                </div>
            </CardFooter>
        </Card>
    );
}

export default ProjectCard;
