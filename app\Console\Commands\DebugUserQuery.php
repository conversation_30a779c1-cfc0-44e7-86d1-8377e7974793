<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class DebugUserQuery extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'debug:user-query';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Debug and clear any sources of invalid user ID queries';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Debugging user ID queries...');

        try {
            // 1. Clear all sessions completely
            $this->info('Clearing all sessions...');
            $sessionCount = DB::table('sessions')->count();
            DB::table('sessions')->truncate();
            $this->info("Cleared {$sessionCount} sessions");

            // 2. Clear all cache
            $this->info('Clearing application cache...');
            $this->call('cache:clear');
            $this->call('config:clear');
            $this->call('route:clear');
            $this->call('view:clear');

            // 3. Check for any invalid data in related tables
            $this->info('Checking for invalid foreign keys...');

            // Check projects with invalid user_id
            $invalidProjects = DB::table('projects')
                ->where('user_id', 'NOT LIKE', '%-%-%-%-%')
                ->count();
            if ($invalidProjects > 0) {
                $this->warn("Found {$invalidProjects} projects with invalid user_id");
            }

            // Check bids with invalid user_id
            $invalidBids = DB::table('bids')
                ->where('user_id', 'NOT LIKE', '%-%-%-%-%')
                ->count();
            if ($invalidBids > 0) {
                $this->warn("Found {$invalidBids} bids with invalid user_id");
            }

            // 4. Regenerate config cache
            $this->info('Regenerating configuration cache...');
            $this->call('config:cache');

            $this->info('Debug cleanup completed!');
            $this->info('');
            $this->info('Next steps for Laravel Cloud:');
            $this->info('1. Restart your application/web server');
            $this->info('2. Clear any external caches (Redis, Memcached)');
            $this->info('3. If issue persists, check for external integrations making queries');

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Failed to debug: '.$e->getMessage());

            return Command::FAILURE;
        }
    }
}
