import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface HealthMetricsProps {
    healthMetrics: {
        project_completion_rate: number;
        user_verification_rate: number;
        bid_acceptance_rate: number;
        average_project_value?: number;
    };
    permissions: {
        can_view_financial_data: boolean;
    };
    formatPercentage: (value: number) => string;
    formatCurrency: (amount: number) => string;
}

export default function HealthMetrics({ healthMetrics, permissions, formatPercentage, formatCurrency }: HealthMetricsProps) {
    return (
        <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Card>
                <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Project Completion Rate</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="text-2xl font-bold text-green-600">{formatPercentage(healthMetrics.project_completion_rate)}</div>
                </CardContent>
            </Card>

            <Card>
                <CardHeader className="pb-2">
                    <CardTitle className="text-sm">User Verification Rate</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="text-2xl font-bold text-blue-600">{formatPercentage(healthMetrics.user_verification_rate)}</div>
                </CardContent>
            </Card>

            <Card>
                <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Bid Acceptance Rate</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="text-2xl font-bold text-purple-600">{formatPercentage(healthMetrics.bid_acceptance_rate)}</div>
                </CardContent>
            </Card>

            {/* Average Project Value - Only for super admins */}
            {permissions.can_view_financial_data && healthMetrics.average_project_value && (
                <Card>
                    <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Avg Project Value</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-orange-600">{formatCurrency(healthMetrics.average_project_value)}</div>
                    </CardContent>
                </Card>
            )}
        </div>
    );
}
