<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class WithdrawalRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() !== null;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'amount' => [
                'required',
                'numeric',
                'min:1',
                'max:'.($this->user()->wallet_balance ?? 0),
                'regex:/^\d+(\.\d{1,2})?$/', // Allow up to 2 decimal places
            ],
            'bank_name' => [
                'required',
                'string',
                'max:255',
                'min:2',
            ],
            'account_number' => [
                'required',
                'string',
                'max:20',
                'min:10',
                'regex:/^[0-9]+$/', // Only numbers
            ],
            'account_name' => [
                'required',
                'string',
                'max:255',
                'min:2',
                'regex:/^[a-zA-Z\s]+$/', // Only letters and spaces
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'amount.required' => 'Please enter the amount you want to withdraw.',
            'amount.numeric' => 'The withdrawal amount must be a valid number.',
            'amount.min' => 'The minimum withdrawal amount is ₵1.00.',
            'amount.max' => 'The withdrawal amount cannot exceed your wallet balance of ₵'.number_format($this->user()->wallet_balance ?? 0, 2).'.',
            'amount.regex' => 'The withdrawal amount can have at most 2 decimal places.',
            'bank_name.required' => 'Please provide your bank name.',
            'bank_name.min' => 'Bank name must be at least 2 characters.',
            'bank_name.max' => 'Bank name is too long.',
            'account_number.required' => 'Please provide your account number.',
            'account_number.min' => 'Account number must be at least 10 digits.',
            'account_number.max' => 'Account number is too long.',
            'account_number.regex' => 'Account number must contain only numbers.',
            'account_name.required' => 'Please provide the account holder name.',
            'account_name.min' => 'Account name must be at least 2 characters.',
            'account_name.max' => 'Account name is too long.',
            'account_name.regex' => 'Account name must contain only letters and spaces.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'amount' => 'withdrawal amount',
            'bank_name' => 'bank name',
            'account_number' => 'account number',
            'account_name' => 'account holder name',
        ];
    }
}
