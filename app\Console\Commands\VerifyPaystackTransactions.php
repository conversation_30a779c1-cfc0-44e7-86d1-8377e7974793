<?php

namespace App\Console\Commands;

use App\Models\WalletTransaction;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class VerifyPaystackTransactions extends Command
{
    protected $signature = 'paystack:verify-transactions 
                            {--pending : Only verify pending transactions}
                            {--reference= : Verify a specific transaction reference}';

    protected $description = 'Verify pending Paystack transactions and update their status';

    public function handle()
    {
        $this->info('Starting Paystack transaction verification...');

        $query = WalletTransaction::where('payment_method', 'paystack');

        if ($this->option('pending')) {
            $query->where('status', 'pending');
        }

        if ($reference = $this->option('reference')) {
            $query->where('payment_reference', $reference);
        }

        $transactions = $query->get();

        if ($transactions->isEmpty()) {
            $this->info('No transactions found to verify.');
            return;
        }

        $this->info("Found {$transactions->count()} transaction(s) to verify.");

        $updated = 0;
        $failed = 0;

        foreach ($transactions as $transaction) {
            try {
                $this->info("Verifying transaction: {$transaction->payment_reference}");

                $response = Http::withHeaders([
                    'Authorization' => 'Bearer ' . config('services.paystack.secret_key'),
                ])->get("https://api.paystack.co/transaction/verify/{$transaction->payment_reference}");

                if (!$response->successful()) {
                    $this->error("Failed to verify transaction {$transaction->payment_reference}: HTTP {$response->status()}");
                    $failed++;
                    continue;
                }

                $data = $response->json();
                $paystackStatus = $data['data']['status'];
                $paystackAmount = $data['data']['amount'] / 100; // Convert from kobo

                $this->line("Paystack status: {$paystackStatus}");
                $this->line("Paystack amount: {$paystackAmount}");
                $this->line("Local status: {$transaction->status}");
                $this->line("Local amount: {$transaction->amount}");

                if ($paystackStatus === 'success' && $transaction->status !== 'completed') {
                    // Update the transaction to completed
                    DB::transaction(function () use ($transaction, $data, $paystackAmount) {
                        $user = $transaction->user;
                        $newBalance = $user->wallet_balance + $paystackAmount;

                        // Update user balance
                        $user->update(['wallet_balance' => $newBalance]);

                        // Update transaction
                        $transaction->update([
                            'status' => 'completed',
                            'balance_after' => $newBalance,
                            'metadata' => array_merge($transaction->metadata ?? [], [
                                'paystack_verification_data' => $data['data'],
                                'verified_at' => now(),
                                'verified_by_command' => true,
                            ]),
                        ]);
                    });

                    $this->info("✓ Transaction {$transaction->payment_reference} updated to completed");
                    $updated++;
                } elseif ($paystackStatus === 'failed' && $transaction->status === 'pending') {
                    // Update transaction to failed
                    $transaction->update([
                        'status' => 'failed',
                        'metadata' => array_merge($transaction->metadata ?? [], [
                            'paystack_verification_data' => $data['data'],
                            'verified_at' => now(),
                            'verified_by_command' => true,
                        ]),
                    ]);

                    $this->info("✓ Transaction {$transaction->payment_reference} updated to failed");
                    $updated++;
                } elseif ($paystackStatus === 'abandoned' && $transaction->status === 'pending') {
                    // Update transaction to failed (abandoned is effectively failed)
                    $transaction->update([
                        'status' => 'failed',
                        'metadata' => array_merge($transaction->metadata ?? [], [
                            'paystack_verification_data' => $data['data'],
                            'verified_at' => now(),
                            'verified_by_command' => true,
                            'abandonment_reason' => $data['data']['gateway_response'] ?? 'Transaction abandoned',
                        ]),
                    ]);

                    $this->info("✓ Transaction {$transaction->payment_reference} marked as failed (abandoned)");
                    $updated++;
                } else {
                    $this->line("- Transaction {$transaction->payment_reference} is already in sync");
                }
            } catch (\Exception $e) {
                $this->error("Error verifying transaction {$transaction->payment_reference}: {$e->getMessage()}");
                Log::error('Paystack verification error', [
                    'transaction_id' => $transaction->id,
                    'reference' => $transaction->payment_reference,
                    'error' => $e->getMessage(),
                ]);
                $failed++;
            }

            $this->newLine();
        }

        $this->info("Verification complete!");
        $this->info("Updated: {$updated}");
        $this->info("Failed: {$failed}");
    }
}
