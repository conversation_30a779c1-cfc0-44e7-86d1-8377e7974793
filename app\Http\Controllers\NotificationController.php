<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class NotificationController extends Controller
{
    /**
     * Display the notifications page.
     */
    public function showPage(Request $request)
    {
        $user = Auth::user();

        $notifications = $user->notifications()
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return Inertia::render('Notifications/Index', [
            'notifications' => $notifications,
            'unread_count' => $user->unreadNotifications()->count(),
        ]);
    }

    /**
     * Get paginated notifications for the authenticated user.
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        if (! $user) {
            abort(401, 'User not authenticated');
        }

        try {
            $notifications = $user->notifications()
                ->orderBy('created_at', 'desc')
                ->paginate(20);

            return Inertia::render('Notifications/Index', [
                'notifications' => $notifications,
                'unread_count' => $user->unreadNotifications()->count(),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch notifications for user: ' . $user->id, [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
            ]);

            abort(500, 'Failed to fetch notifications');
        }
    }

    /**
     * Get notifications for the dropdown (limited to 10 most recent).
     */
    public function dropdown()
    {
        $user = Auth::user();

        if (! $user) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated',
                'notifications' => [],
                'unread_count' => 0,
            ], 401);
        }

        try {
            $notifications = $user->notifications()
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get()
                ->map(function ($notification) {
                    $data = $notification->data;

                    return [
                        'id' => $notification->id,
                        'title' => $data['title'] ?? 'Notification',
                        'message' => $data['message'] ?? '',
                        'time' => $notification->created_at->diffForHumans(),
                        'read' => $notification->read_at !== null,
                        'type' => $this->mapNotificationType($data['type'] ?? 'info'),
                        'action_url' => $data['action_url'] ?? '#',
                        'created_at' => $notification->created_at->toISOString(),
                    ];
                });

            return response()->json([
                'success' => true,
                'notifications' => $notifications,
                'unread_count' => $user->unreadNotifications()->count(),
            ]);
        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error('Failed to fetch notifications for user: ' . $user->id, [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'user_id_type' => gettype($user->id),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch notifications',
                'notifications' => [],
                'unread_count' => 0,
            ], 500);
        }
    }

    /**
     * Mark a specific notification as read.
     */
    public function markAsRead(string $id)
    {
        $user = Auth::user();

        $notification = $user->notifications()->find($id);

        if (! $notification) {
            return response()->json([
                'success' => false,
                'message' => 'Notification not found.',
            ], 404);
        }

        $notification->markAsRead();

        return response()->json([
            'success' => true,
            'message' => 'Notification marked as read.',
            'unread_count' => $user->unreadNotifications()->count(),
        ]);
    }

    /**
     * Mark all notifications as read.
     */
    public function markAllAsRead()
    {
        $user = Auth::user();

        $user->unreadNotifications()->update(['read_at' => now()]);

        return response()->json([
            'success' => true,
            'message' => 'All notifications marked as read.',
            'unread_count' => 0,
        ]);
    }

    /**
     * Delete a specific notification.
     */
    public function destroy(string $id)
    {
        $user = Auth::user();

        $notification = $user->notifications()->find($id);

        if (! $notification) {
            return response()->json([
                'success' => false,
                'message' => 'Notification not found.',
            ], 404);
        }

        $notification->delete();

        return response()->json([
            'success' => true,
            'message' => 'Notification deleted.',
            'unread_count' => $user->unreadNotifications()->count(),
        ]);
    }

    /**
     * Get unread notification count.
     */
    public function unreadCount()
    {
        $user = Auth::user();

        return response()->json([
            'success' => true,
            'unread_count' => $user->unreadNotifications()->count(),
        ]);
    }

    /**
     * Map notification types to frontend-compatible types.
     */
    private function mapNotificationType(string $type): string
    {
        return match ($type) {
            'bid_submitted' => 'info',
            'bid_accepted' => 'success',
            'bid_rejected' => 'warning',
            'milestone_completed' => 'info',
            'payment_released' => 'success',
            'project_completed' => 'success',
            default => 'info',
        };
    }
}
