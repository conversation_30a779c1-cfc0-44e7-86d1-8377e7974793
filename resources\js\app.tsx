import '../css/app.css';

import { createInertiaApp, router } from '@inertiajs/react';
import axios from 'axios';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { createRoot } from 'react-dom/client';
import { Toaster } from './components/ui/toaster';
import { initializeTheme } from './hooks/use-appearance';

// Configure axios defaults
axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
if (token) {
    axios.defaults.headers.common['X-CSRF-TOKEN'] = token;
}

// Global error handler for 419 CSRF errors
router.on('error', (event) => {
    if (event.detail.errors && event.detail.errors.message === 'CSRF token mismatch.') {
        // Show a user-friendly message and refresh the page to get a new token
        console.log('CSRF token expired, refreshing page...');
        window.location.reload();
        return false; // Prevent default error handling
    }
});

// Global Inertia error handler for HTTP errors
document.addEventListener('inertia:error', (event: any) => {
    if (event.detail.response?.status === 419) {
        console.log('419 error detected, refreshing page...');
        window.location.reload();
    }
});

const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

createInertiaApp({
    title: (title) => (title ? `${title} - ${appName}` : appName),
    resolve: (name) => resolvePageComponent(`./pages/${name}.tsx`, import.meta.glob('./pages/**/*.tsx')),
    setup({ el, App, props }) {
        const root = createRoot(el);

        root.render(
            <>
                <App {...props} />
                <Toaster />
            </>,
        );
    },
    progress: {
        color: '#4B5563',
    },
});

// This will set light / dark mode on load...
initializeTheme();
