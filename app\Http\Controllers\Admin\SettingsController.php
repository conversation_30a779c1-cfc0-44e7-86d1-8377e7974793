<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PlatformSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class SettingsController extends Controller
{
    public function __construct()
    {
        // Only super admins can access settings
        $this->middleware('super_admin');
    }

    /**
     * Display the settings dashboard
     */
    public function index()
    {
        $settings = [
            'commission_rate' => PlatformSetting::getCommissionRate(),
            'site_name' => PlatformSetting::get('site_name', 'TheSyLink'),
            'maintenance_mode' => PlatformSetting::get('maintenance_mode', false),
        ];

        return Inertia::render('Admin/Settings', [
            'settings' => $settings,
            'permissions' => [
                'can_edit_settings' => Auth::user()->isSuperAdmin(),
            ],
        ]);
    }

    /**
     * Update commission rate
     */
    public function updateCommissionRate(Request $request)
    {
        $validated = $request->validate([
            'commission_rate' => 'required|numeric|min:0|max:100',
        ]);

        PlatformSetting::setCommissionRate((float) $validated['commission_rate']);

        return back()->with('success', 'Commission rate updated successfully.');
    }

    /**
     * Update site settings
     */
    public function updateSiteSettings(Request $request)
    {
        $validated = $request->validate([
            'site_name' => 'required|string|max:255',
            'maintenance_mode' => 'boolean',
        ]);

        PlatformSetting::set('site_name', $validated['site_name'], 'string', 'Platform name displayed throughout the site');
        PlatformSetting::set('maintenance_mode', $validated['maintenance_mode'] ?? false, 'boolean', 'Whether the platform is in maintenance mode');

        return back()->with('success', 'Site settings updated successfully.');
    }

    /**
     * Get current commission rate (API endpoint)
     */
    public function getCommissionRate()
    {
        return response()->json([
            'commission_rate' => PlatformSetting::getCommissionRate(),
        ]);
    }
}
