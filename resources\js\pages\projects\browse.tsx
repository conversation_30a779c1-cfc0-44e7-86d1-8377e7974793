import ProjectCard from '@/components/project-card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router, usePage } from '@inertiajs/react';
import { Filter, Search } from 'lucide-react';
import { useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Browse Projects',
        href: '/browse',
    },
];

interface Project {
    id: number;
    title: string;
    description: string;
    budget_min?: number;
    budget_max?: number;
    budget_type: 'fixed' | 'negotiable';
    deadline?: string;
    category?: string;
    academic_level?: string;
    status: 'open' | 'in_progress' | 'completed' | 'cancelled';
    file_count: number;
    created_at: string;
    user: {
        id: number;
        name: string;
    };
}

interface PaginationData {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    data: Project[];
}

interface BrowseProjectsProps {
    projects: PaginationData;
}

const categories = ['All Categories', 'Computer Science', 'Nursing', 'Business Administration', 'Engineering'];

const academicLevels = ['All Levels', 'undergraduate', 'postgraduate'];

const budgetRanges = [
    { label: 'All Budgets', value: 'all' },
    { label: '₵500 - ₵1000', value: '500-1000' },
    { label: '₵1000 - ₵2500', value: '1000-2500' },
    { label: 'Over ₵2500', value: '2500-999999' },
];

export default function BrowseProjects({ projects }: BrowseProjectsProps) {
    // Initialize all filter states from current URL so Inertia navigations don't clear the inputs
    const page = usePage();
    const getUrlParams = () => {
        const url = (page && page.url) || (typeof window !== 'undefined' ? window.location.href : '');
        return new URLSearchParams(url.split('?')[1] ?? '');
    };

    const params = getUrlParams();
    const [searchTerm, setSearchTerm] = useState(params.get('search') ?? '');
    const [selectedCategory, setSelectedCategory] = useState(params.get('category') ?? 'All Categories');
    const [selectedLevel, setSelectedLevel] = useState(params.get('academic_level') ?? 'All Levels');
    const [selectedBudget, setSelectedBudget] = useState(params.get('budget_range') ?? 'all');

    const handleSearch = () => {
        const params = new URLSearchParams();

        if (searchTerm) params.append('search', searchTerm);
        if (selectedCategory !== 'All Categories') params.append('category', selectedCategory);
        if (selectedLevel !== 'All Levels') params.append('academic_level', selectedLevel);
        if (selectedBudget && selectedBudget !== 'all') params.append('budget_range', selectedBudget);

        router.get('/browse', Object.fromEntries(params));
    };

    // Handle filter changes when filters actually change
    const handleFilterChange = (type: string, value: string) => {
        // Update state first
        if (type === 'category') {
            setSelectedCategory(value);
        } else if (type === 'level') {
            setSelectedLevel(value);
        } else if (type === 'budget') {
            setSelectedBudget(value);
        }

        // Build params with the new value
        const params = new URLSearchParams();
        if (searchTerm) params.append('search', searchTerm);

        // Use the new value for the type being changed, existing state for others
        if (type === 'category') {
            if (value !== 'All Categories') params.append('category', value);
        } else if (selectedCategory !== 'All Categories') {
            params.append('category', selectedCategory);
        }

        if (type === 'level') {
            if (value !== 'All Levels') params.append('academic_level', value);
        } else if (selectedLevel !== 'All Levels') {
            params.append('academic_level', selectedLevel);
        }

        if (type === 'budget') {
            if (value !== 'all') params.append('budget_range', value);
        } else if (selectedBudget !== 'all') {
            params.append('budget_range', selectedBudget);
        }

        router.get('/browse', Object.fromEntries(params));
    };

    const clearFilters = () => {
        setSearchTerm('');
        setSelectedCategory('All Categories');
        setSelectedLevel('All Levels');
        setSelectedBudget('all');
        router.get('/browse');
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Browse Projects" />

            <div className="space-y-6">
                {/* Header */}
                <div className="text-center">
                    <h1 className="text-3xl font-bold">Browse Projects</h1>
                    <p className="mt-2 text-muted-foreground">Find exciting academic projects to work on and showcase your expertise</p>
                </div>

                {/* Search and Filters */}
                <div className="rounded-lg border bg-card p-6">
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                        {/* Search */}
                        <div className="lg:col-span-2">
                            <div className="relative">
                                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                                <Input
                                    placeholder="Search projects..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    onKeyDown={(e) => {
                                        if (e.key === 'Enter') {
                                            handleSearch();
                                        }
                                    }}
                                    className="pl-10"
                                />
                            </div>
                        </div>

                        {/* Category Filter */}
                        <div>
                            <Select value={selectedCategory} onValueChange={(value) => handleFilterChange('category', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Category" />
                                </SelectTrigger>
                                <SelectContent>
                                    {categories.map((category) => (
                                        <SelectItem key={category} value={category}>
                                            {category}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        {/* Academic Level Filter */}
                        <div>
                            <Select value={selectedLevel} onValueChange={(value) => handleFilterChange('level', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Level" />
                                </SelectTrigger>
                                <SelectContent>
                                    {academicLevels.map((level) => (
                                        <SelectItem key={level} value={level}>
                                            {level}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        {/* Budget Filter */}
                        <div>
                            <Select value={selectedBudget} onValueChange={(value) => handleFilterChange('budget', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Budget" />
                                </SelectTrigger>
                                <SelectContent>
                                    {budgetRanges.map((range) => (
                                        <SelectItem key={range.value} value={range.value}>
                                            {range.label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    {/* Filter Actions */}
                    <div className="mt-4 flex gap-2">
                        <Button onClick={handleSearch}>
                            <Filter className="mr-2 h-4 w-4" />
                            Apply Filters
                        </Button>
                        <Button variant="outline" onClick={clearFilters}>
                            Clear All
                        </Button>
                    </div>
                </div>

                {/* Results */}
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <p className="text-muted-foreground">
                            Showing {projects.data.length} of {projects.total} projects
                        </p>
                    </div>

                    {/* Projects Grid */}
                    {projects.data.length > 0 ? (
                        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                            {projects.data.map((project) => (
                                <ProjectCard key={project.id} project={project} showAuthor={true} />
                            ))}
                        </div>
                    ) : (
                        <div className="py-12 text-center">
                            <div className="mx-auto max-w-md">
                                <div className="mx-auto h-12 w-12 text-muted-foreground/50">
                                    <Search className="h-full w-full" />
                                </div>
                                <h3 className="mt-4 text-lg font-semibold">No projects found</h3>
                                <p className="mt-2 text-muted-foreground">Try adjusting your search criteria or check back later for new projects.</p>
                                <Button className="mt-4" variant="outline" onClick={clearFilters}>
                                    Clear Filters
                                </Button>
                            </div>
                        </div>
                    )}

                    {/* Pagination */}
                    {projects.last_page > 1 && (
                        <div className="mt-8 flex items-center justify-center space-x-2">
                            {projects.current_page > 1 && (
                                <Button variant="outline" onClick={() => router.get('/browse', { page: projects.current_page - 1 })}>
                                    Previous
                                </Button>
                            )}

                            <span className="text-sm text-muted-foreground">
                                Page {projects.current_page} of {projects.last_page}
                            </span>

                            {projects.current_page < projects.last_page && (
                                <Button variant="outline" onClick={() => router.get('/browse', { page: projects.current_page + 1 })}>
                                    Next
                                </Button>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
