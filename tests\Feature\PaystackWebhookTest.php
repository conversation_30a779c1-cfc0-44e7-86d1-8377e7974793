<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\WalletTransaction;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class PaystackWebhookTest extends TestCase
{
    use RefreshDatabase;

    private string $secretKey = 'sk_test_webhook_secret_key';

    protected function setUp(): void
    {
        parent::setUp();
        Config::set('services.paystack.secret_key', $this->secretKey);
    }

    public function test_webhook_rejects_requests_without_signature()
    {
        $payload = [
            'event' => 'charge.success',
            'data' => [
                'reference' => 'test_ref_123',
                'amount' => 5000, // 50.00 in kobo
            ],
        ];

        $response = $this->postJson('/api/webhooks/paystack', $payload);

        $response->assertStatus(400)
            ->assertJson(['error' => 'Invalid signature']);
    }

    public function test_webhook_rejects_requests_with_invalid_signature()
    {
        $payload = [
            'event' => 'charge.success',
            'data' => [
                'reference' => 'test_ref_123',
                'amount' => 5000,
            ],
        ];

        $response = $this->postJson('/webhooks/paystack', $payload, [
            'X-Paystack-Signature' => 'invalid_signature',
        ]);

        $response->assertStatus(400)
            ->assertJson(['error' => 'Invalid signature']);
    }

    public function test_webhook_handles_charge_success_event_successfully()
    {
        // Create a user and pending transaction
        $user = User::factory()->create(['wallet_balance' => 100.00]);
        $reference = 'dep_test_' . time() . '_' . $user->id;

        $transaction = WalletTransaction::create([
            'user_id' => $user->id,
            'transaction_id' => $reference,
            'type' => 'deposit',
            'amount' => 50.00,
            'balance_before' => 100.00,
            'balance_after' => 100.00, // Will be updated by webhook
            'status' => 'pending',
            'payment_method' => 'paystack',
            'payment_reference' => $reference,
            'description' => 'Test deposit',
        ]);

        $payload = [
            'event' => 'charge.success',
            'data' => [
                'reference' => $reference,
                'amount' => 5000, // 50.00 in kobo
                'status' => 'success',
                'customer' => [
                    'email' => $user->email,
                ],
            ],
        ];

        $signature = $this->generateValidSignature($payload);

        $response = $this->postJson('/webhooks/paystack', $payload, [
            'X-Paystack-Signature' => $signature,
        ]);

        $response->assertStatus(200)
            ->assertJson(['status' => 'success']);

        // Verify user balance was updated
        $user->refresh();
        $this->assertEquals(150.00, $user->wallet_balance);

        // Verify transaction was completed
        $transaction->refresh();
        $this->assertEquals('completed', $transaction->status);
        $this->assertEquals(150.00, $transaction->balance_after);
        $this->assertArrayHasKey('paystack_webhook_data', $transaction->metadata);
        $this->assertArrayHasKey('completed_at', $transaction->metadata);
    }

    public function test_webhook_handles_charge_failed_event_successfully()
    {
        // Create a user and pending transaction
        $user = User::factory()->create(['wallet_balance' => 100.00]);
        $reference = 'dep_test_' . time() . '_' . $user->id;

        $transaction = WalletTransaction::create([
            'user_id' => $user->id,
            'transaction_id' => $reference,
            'type' => 'deposit',
            'amount' => 50.00,
            'balance_before' => 100.00,
            'balance_after' => 100.00,
            'status' => 'pending',
            'payment_method' => 'paystack',
            'payment_reference' => $reference,
            'description' => 'Test deposit',
        ]);

        $payload = [
            'event' => 'charge.failed',
            'data' => [
                'reference' => $reference,
                'amount' => 5000,
                'status' => 'failed',
                'customer' => [
                    'email' => $user->email,
                ],
            ],
        ];

        $signature = $this->generateValidSignature($payload);

        Log::shouldReceive('info')
            ->with('Paystack webhook received', [
                'event' => 'charge.failed',
                'reference' => $reference,
            ])
            ->once();

        Log::shouldReceive('info')
            ->with('Paystack webhook: Deposit failed', \Mockery::type('array'))
            ->once();

        $response = $this->postJson('/webhooks/paystack', $payload, [
            'X-Paystack-Signature' => $signature,
        ]);

        $response->assertStatus(200)
            ->assertJson(['status' => 'success']);

        // Verify user balance was NOT updated
        $user->refresh();
        $this->assertEquals(100.00, $user->wallet_balance);

        // Verify transaction was marked as failed
        $transaction->refresh();
        $this->assertEquals('failed', $transaction->status);
        $this->assertEquals(100.00, $transaction->balance_after); // Unchanged
        $this->assertArrayHasKey('paystack_webhook_data', $transaction->metadata);
        $this->assertArrayHasKey('failed_at', $transaction->metadata);
    }

    public function test_webhook_handles_nonexistent_transaction_gracefully()
    {
        $payload = [
            'event' => 'charge.success',
            'data' => [
                'reference' => 'nonexistent_reference',
                'amount' => 5000,
                'status' => 'success',
            ],
        ];

        $signature = $this->generateValidSignature($payload);

        Log::shouldReceive('info')
            ->with('Paystack webhook received', [
                'event' => 'charge.success',
                'reference' => 'nonexistent_reference',
            ])
            ->once();

        Log::shouldReceive('warning')
            ->with('Paystack webhook: Transaction not found', ['reference' => 'nonexistent_reference'])
            ->once();

        $response = $this->postJson('/webhooks/paystack', $payload, [
            'X-Paystack-Signature' => $signature,
        ]);

        $response->assertStatus(200)
            ->assertJson(['status' => 'success']);
    }

    public function test_webhook_handles_unhandled_events()
    {
        $payload = [
            'event' => 'subscription.create',
            'data' => [
                'reference' => 'some_reference',
            ],
        ];

        $signature = $this->generateValidSignature($payload);

        Log::shouldReceive('info')
            ->with('Paystack webhook received', [
                'event' => 'subscription.create',
                'reference' => 'some_reference',
            ])
            ->once();

        Log::shouldReceive('info')
            ->with('Unhandled Paystack webhook event', ['event' => 'subscription.create'])
            ->once();

        $response = $this->postJson('/webhooks/paystack', $payload, [
            'X-Paystack-Signature' => $signature,
        ]);

        $response->assertStatus(200)
            ->assertJson(['status' => 'success']);
    }

    public function test_webhook_handles_processing_exceptions_gracefully()
    {
        // Create a user but no transaction (this will cause an exception in processing)
        $user = User::factory()->create(['wallet_balance' => 100.00]);
        $reference = 'dep_test_' . time() . '_' . $user->id;

        $payload = [
            'event' => 'charge.success',
            'data' => [
                'reference' => $reference,
                'amount' => 5000,
                'status' => 'success',
            ],
        ];

        $signature = $this->generateValidSignature($payload);

        Log::shouldReceive('info')
            ->with('Paystack webhook received', [
                'event' => 'charge.success',
                'reference' => $reference,
            ])
            ->once();

        Log::shouldReceive('warning')
            ->with('Paystack webhook: Transaction not found', ['reference' => $reference])
            ->once();

        $response = $this->postJson('/webhooks/paystack', $payload, [
            'X-Paystack-Signature' => $signature,
        ]);

        $response->assertStatus(200)
            ->assertJson(['status' => 'success']);
    }

    public function test_webhook_ignores_already_completed_transactions()
    {
        // Create a user and already completed transaction
        $user = User::factory()->create(['wallet_balance' => 150.00]);
        $reference = 'dep_test_' . time() . '_' . $user->id;

        $transaction = WalletTransaction::create([
            'user_id' => $user->id,
            'transaction_id' => $reference,
            'type' => 'deposit',
            'amount' => 50.00,
            'balance_before' => 100.00,
            'balance_after' => 150.00,
            'status' => 'completed', // Already completed
            'payment_method' => 'paystack',
            'payment_reference' => $reference,
            'description' => 'Test deposit',
        ]);

        $payload = [
            'event' => 'charge.success',
            'data' => [
                'reference' => $reference,
                'amount' => 5000,
                'status' => 'success',
            ],
        ];

        $signature = $this->generateValidSignature($payload);

        Log::shouldReceive('info')
            ->with('Paystack webhook received', [
                'event' => 'charge.success',
                'reference' => $reference,
            ])
            ->once();

        Log::shouldReceive('warning')
            ->with('Paystack webhook: Transaction not found', ['reference' => $reference])
            ->once();

        $response = $this->postJson('/webhooks/paystack', $payload, [
            'X-Paystack-Signature' => $signature,
        ]);

        $response->assertStatus(200)
            ->assertJson(['status' => 'success']);

        // Verify balance wasn't double-credited
        $user->refresh();
        $this->assertEquals(150.00, $user->wallet_balance);

        // Verify transaction status didn't change
        $transaction->refresh();
        $this->assertEquals('completed', $transaction->status);
    }

    /**
     * Generate a valid HMAC signature for the given payload.
     */
    private function generateValidSignature(array $payload): string
    {
        $jsonPayload = json_encode($payload);

        return hash_hmac('sha512', $jsonPayload, $this->secretKey);
    }
}
