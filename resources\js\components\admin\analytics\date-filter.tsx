import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Filter } from 'lucide-react';

interface DateFilterProps {
    data: {
        start_date: string;
        end_date: string;
    };
    setData: (key: string, value: string) => void;
    onFilterChange: () => void;
}

export default function DateFilter({ data, setData, onFilterChange }: DateFilterProps) {
    return (
        <Card className="mb-8">
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Filter className="h-5 w-5" />
                    Date Range Filter
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div className="flex flex-col gap-4 sm:flex-row sm:items-end">
                    <div className="flex-1">
                        <Label htmlFor="start_date">Start Date</Label>
                        <Input id="start_date" type="date" value={data.start_date} onChange={(e) => setData('start_date', e.target.value)} />
                    </div>
                    <div className="flex-1">
                        <Label htmlFor="end_date">End Date</Label>
                        <Input id="end_date" type="date" value={data.end_date} onChange={(e) => setData('end_date', e.target.value)} />
                    </div>
                    <Button onClick={onFilterChange} className="sm:min-w-[120px]">
                        Apply Filter
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}
