import { <PERSON><PERSON>, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import AppLayout from '@/layouts/app-layout';
import { PageProps } from '@/types';
import { Head, router } from '@inertiajs/react';
import { Bell, Briefcase, Check, Filter, Mail, MessageSquare, Trash2 } from 'lucide-react';
import { useState } from 'react';

interface Notification {
    id: string;
    type: string;
    data: {
        title: string;
        message: string;
        action_url?: string;
        icon?: string;
        sender?: {
            name: string;
            avatar?: string;
        };
    };
    read_at: string | null;
    created_at: string;
}

interface NotificationsIndexProps extends PageProps {
    notifications: {
        data: Notification[];
        total: number;
        unread_count: number;
    };
}

export default function NotificationsIndex({ auth, notifications }: NotificationsIndexProps) {
    const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
    const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all');

    const filteredNotifications = notifications.data.filter((notification) => {
        if (filter === 'unread') return !notification.read_at;
        if (filter === 'read') return !!notification.read_at;
        return true;
    });

    const handleSelectAll = () => {
        if (selectedNotifications.length === filteredNotifications.length) {
            setSelectedNotifications([]);
        } else {
            setSelectedNotifications(filteredNotifications.map((n) => n.id));
        }
    };

    const handleSelectNotification = (notificationId: string) => {
        if (selectedNotifications.includes(notificationId)) {
            setSelectedNotifications((prev) => prev.filter((id) => id !== notificationId));
        } else {
            setSelectedNotifications((prev) => [...prev, notificationId]);
        }
    };

    const markAsRead = async (notificationIds: string[]) => {
        try {
            await router.patch(
                '/notifications/read',
                {
                    notification_ids: notificationIds,
                },
                {
                    preserveState: true,
                    onSuccess: () => {
                        setSelectedNotifications([]);
                    },
                },
            );
        } catch (error) {
            console.error('Failed to mark notifications as read:', error);
        }
    };

    const deleteNotifications = async (notificationIds: string[]) => {
        if (confirm('Are you sure you want to delete the selected notifications?')) {
            try {
                await router.delete('/notifications', {
                    data: { notification_ids: notificationIds },
                    preserveState: true,
                    onSuccess: () => {
                        setSelectedNotifications([]);
                    },
                });
            } catch (error) {
                console.error('Failed to delete notifications:', error);
            }
        }
    };

    const getNotificationIcon = (type: string) => {
        switch (type) {
            case 'bid_submitted':
            case 'bid_accepted':
            case 'bid_rejected':
                return <Briefcase className="h-5 w-5" />;
            case 'message_received':
                return <MessageSquare className="h-5 w-5" />;
            case 'email_notification':
                return <Mail className="h-5 w-5" />;
            default:
                return <Bell className="h-5 w-5" />;
        }
    };

    const formatTimeAgo = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

        if (diffInMinutes < 1) return 'Just now';
        if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours < 24) return `${diffInHours}h ago`;

        const diffInDays = Math.floor(diffInHours / 24);
        if (diffInDays < 7) return `${diffInDays}d ago`;

        return date.toLocaleDateString();
    };

    return (
        <AppLayout>
            <Head title="Notifications" />

            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <Bell className="h-6 w-6" />
                        <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200">Notifications</h1>
                        {notifications.unread_count > 0 && <Badge variant="destructive">{notifications.unread_count} unread</Badge>}
                    </div>

                    <div className="flex items-center gap-2">
                        {selectedNotifications.length > 0 && (
                            <>
                                <Button onClick={() => markAsRead(selectedNotifications)} variant="outline" size="sm">
                                    <Check className="mr-2 h-4 w-4" />
                                    Mark as Read
                                </Button>
                                <Button onClick={() => deleteNotifications(selectedNotifications)} variant="outline" size="sm">
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Delete
                                </Button>
                            </>
                        )}
                    </div>
                </div>

                <div className="py-12">
                    <div className="mx-auto max-w-4xl sm:px-6 lg:px-8">
                        <Card>
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <CardTitle>All Notifications ({notifications.total})</CardTitle>

                                    <div className="flex items-center gap-4">
                                        {/* Filter */}
                                        <div className="flex items-center gap-2">
                                            <Filter className="h-4 w-4" />
                                            <select
                                                value={filter}
                                                onChange={(e) => setFilter(e.target.value as 'all' | 'unread' | 'read')}
                                                className="rounded border px-2 py-1 text-sm"
                                            >
                                                <option value="all">All</option>
                                                <option value="unread">Unread</option>
                                                <option value="read">Read</option>
                                            </select>
                                        </div>

                                        {/* Select All */}
                                        {filteredNotifications.length > 0 && (
                                            <div className="flex items-center gap-2">
                                                <Checkbox
                                                    checked={selectedNotifications.length === filteredNotifications.length}
                                                    onCheckedChange={handleSelectAll}
                                                />
                                                <span className="text-sm">Select All</span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent>
                                {filteredNotifications.length > 0 ? (
                                    <div className="space-y-4">
                                        {filteredNotifications.map((notification) => (
                                            <div
                                                key={notification.id}
                                                className={`rounded-lg border p-4 transition-colors ${
                                                    !notification.read_at
                                                        ? 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20'
                                                        : 'border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800'
                                                } ${selectedNotifications.includes(notification.id) ? 'ring-2 ring-blue-500' : ''}`}
                                            >
                                                <div className="flex items-start gap-4">
                                                    <Checkbox
                                                        checked={selectedNotifications.includes(notification.id)}
                                                        onCheckedChange={() => handleSelectNotification(notification.id)}
                                                    />

                                                    <div className="flex-1">
                                                        <div className="flex items-start gap-3">
                                                            <div className="rounded-full bg-gray-100 p-2 dark:bg-gray-700">
                                                                {getNotificationIcon(notification.type)}
                                                            </div>

                                                            <div className="flex-1">
                                                                <div className="flex items-center justify-between">
                                                                    <h4 className="font-medium text-gray-900 dark:text-gray-100">
                                                                        {notification.data.title}
                                                                    </h4>
                                                                    <div className="flex items-center gap-2">
                                                                        {!notification.read_at && (
                                                                            <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                                                                        )}
                                                                        <span className="text-sm text-gray-500 dark:text-gray-400">
                                                                            {formatTimeAgo(notification.created_at)}
                                                                        </span>
                                                                    </div>
                                                                </div>

                                                                <p className="mt-1 text-gray-600 dark:text-gray-400">{notification.data.message}</p>

                                                                {notification.data.sender && (
                                                                    <div className="mt-2 flex items-center gap-2">
                                                                        <Avatar className="h-6 w-6">
                                                                            <AvatarFallback className="text-xs">
                                                                                {notification.data.sender.name.charAt(0).toUpperCase()}
                                                                            </AvatarFallback>
                                                                        </Avatar>
                                                                        <span className="text-sm text-gray-600 dark:text-gray-400">
                                                                            {notification.data.sender.name}
                                                                        </span>
                                                                    </div>
                                                                )}

                                                                {notification.data.action_url && (
                                                                    <Button
                                                                        variant="link"
                                                                        className="mt-2 h-auto p-0"
                                                                        onClick={() => router.visit(notification.data.action_url!)}
                                                                    >
                                                                        View Details →
                                                                    </Button>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="py-12 text-center">
                                        <Bell className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                                        <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-gray-100">No notifications found</h3>
                                        <p className="text-gray-600 dark:text-gray-400">
                                            {filter === 'unread'
                                                ? "You don't have any unread notifications."
                                                : filter === 'read'
                                                  ? "You don't have any read notifications."
                                                  : "You don't have any notifications yet."}
                                        </p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
