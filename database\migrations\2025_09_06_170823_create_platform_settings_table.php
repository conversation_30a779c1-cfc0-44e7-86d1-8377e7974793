<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('platform_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique(); // e.g., 'commission_rate'
            $table->text('value'); // Store the actual value
            $table->string('type')->default('string'); // 'string', 'integer', 'float', 'boolean', 'json'
            $table->text('description')->nullable(); // Human-readable description
            $table->boolean('is_public')->default(false); // Whether this setting can be viewed by non-admins
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('platform_settings');
    }
};
