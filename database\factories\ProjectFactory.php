<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Project>
 */
class ProjectFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = fake()->sentence(rand(3, 8));
        $categories = [
            'Computer Science',
            'Nursing',
            'Business Administration',
            'Engineering'
        ];

        $academicLevels = ['undergraduate', 'graduate', 'postgraduate'];
        $budgetTypes = ['fixed'];

        $budgetMin = rand(200, 1000);
        $budgetMax = $budgetMin + rand(200, 1000);

        return [
            'title' => rtrim($title, '.'),
            'slug' => fake()->slug() . '-' . fake()->randomNumber(6),
            'description' => fake()->paragraphs(rand(2, 4), true),
            'requirements' => fake()->paragraphs(rand(1, 3), true),
            'budget_min' => $budgetMin,
            'budget_max' => $budgetMax,
            'budget_type' => fake()->randomElement($budgetTypes),
            'deadline' => fake()->dateTimeBetween('+1 week', '+6 months'),
            'category' => fake()->randomElement($categories),
            'academic_level' => fake()->randomElement($academicLevels),
            'status' => fake()->randomElement(['open', 'open', 'open', 'in_progress', 'completed']),
            'file_count' => 0,
        ];
    }
}
