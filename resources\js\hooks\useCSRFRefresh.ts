import { router } from '@inertiajs/react';
import { useEffect, useRef } from 'react';

/**
 * Hook to automatically refresh the page when CSRF token expires
 * This prevents 419 Page Expired errors on forms
 */
export function useCSRFRefresh(intervalMinutes: number = 30) {
    const intervalRef = useRef<NodeJS.Timeout | null>(null);

    useEffect(() => {
        // Clear any existing interval
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
        }

        // Set up automatic page refresh before token expires
        intervalRef.current = setInterval(() => {
            // Only refresh if we're on auth pages (login, register, etc.)
            const currentPath = window.location.pathname;
            const authPages = ['/login', '/register', '/forgot-password', '/reset-password'];
            
            if (authPages.some(path => currentPath.includes(path))) {
                // Soft refresh to get new CSRF token without losing form data
                router.reload({ only: ['csrf_token'] });
            }
        }, intervalMinutes * 60 * 1000); // Convert minutes to milliseconds

        // Cleanup on unmount
        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }
        };
    }, [intervalMinutes]);

    // Also refresh on focus if it's been a while
    useEffect(() => {
        const handleFocus = () => {
            const currentPath = window.location.pathname;
            const authPages = ['/login', '/register', '/forgot-password', '/reset-password'];
            
            if (authPages.some(path => currentPath.includes(path))) {
                // Check if page has been idle for more than 10 minutes
                const lastActivity = localStorage.getItem('lastActivity');
                const now = Date.now();
                const tenMinutes = 10 * 60 * 1000;
                
                if (!lastActivity || (now - parseInt(lastActivity)) > tenMinutes) {
                    router.reload({ only: ['csrf_token'] });
                }
            }
            
            localStorage.setItem('lastActivity', Date.now().toString());
        };

        window.addEventListener('focus', handleFocus);
        window.addEventListener('click', handleFocus);
        window.addEventListener('keydown', handleFocus);

        return () => {
            window.removeEventListener('focus', handleFocus);
            window.removeEventListener('click', handleFocus);
            window.removeEventListener('keydown', handleFocus);
        };
    }, []);
}
