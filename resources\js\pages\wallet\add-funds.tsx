import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import { Head, router, usePage } from '@inertiajs/react';
import { ArrowLeft, CreditCard, DollarSign, Shield } from 'lucide-react';
import { FormEvent, useState } from 'react';

// Declare Paystack types
declare global {
    interface Window {
        PaystackPop: any;
    }
}

interface Props {
    paystack_public_key: string;
}

interface SharedData {
    // Allow extra shared props (satisfies Inertia PageProps index signature requirement)
    [key: string]: any;

    auth: {
        user: {
            id: number;
            name: string;
            email: string;
            wallet_balance: number;
        };
    };
}

const breadcrumbs = [
    { title: 'Wallet', href: '/wallet/balance' },
    { title: 'Add Funds', href: '/wallet/add-funds' },
];

export default function AddFunds({ paystack_public_key }: Props) {
    const { auth } = usePage<SharedData>().props;
    const [isProcessing, setIsProcessing] = useState(false);
    const [amount, setAmount] = useState('');
    const [email, setEmail] = useState(auth.user.email);

    const handleSubmit = async (e: FormEvent) => {
        e.preventDefault();

        if (isProcessing || !amount || parseFloat(amount) < 1) return;

        setIsProcessing(true);

        try {
            // Get CSRF token more reliably
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

            if (!csrfToken) {
                console.error('CSRF token not found');
                alert('Security token missing. Please refresh the page and try again.');
                setIsProcessing(false);
                return;
            }

            console.log('Initializing payment with route:', route('wallet.deposit.initialize'));

            // Initialize payment on backend
            const response = await fetch(route('wallet.deposit.initialize'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest',
                    Accept: 'application/json',
                },
                credentials: 'same-origin',
                body: JSON.stringify({
                    amount: parseFloat(amount),
                    email: email,
                }),
            });

            // Check if response is ok
            if (!response.ok) {
                console.error('HTTP Error:', response.status, response.statusText);

                // Try to get error message from response
                let errorMessage = 'Payment initialization failed. Please try again.';
                try {
                    const errorData = await response.json();
                    errorMessage = errorData.message || errorMessage;
                } catch (e) {
                    // If response is not JSON (like HTML error page), show generic message
                    console.error('Failed to parse error response:', e);
                    if (response.status === 419) {
                        errorMessage = 'Security token expired. Please refresh the page and try again.';
                    }
                }

                setIsProcessing(false);
                alert(errorMessage);
                return;
            }

            const data = await response.json();

            if (data.success) {
                // Use Paystack Popup
                const popup = new window.PaystackPop();
                popup.resumeTransaction(data.access_code, {
                    onSuccess: (transaction: any) => {
                        // Payment successful, redirect to transactions page
                        router.visit('/wallet/transactions', {
                            onSuccess: () => {
                                // Show success message (will be handled by the callback route)
                            },
                        });
                    },
                    onCancel: () => {
                        setIsProcessing(false);
                        // User cancelled payment
                        console.log('Payment cancelled');
                    },
                    onError: (error: any) => {
                        setIsProcessing(false);
                        console.error('Payment error:', error);
                    },
                });
            } else {
                setIsProcessing(false);
                alert(data.message || 'Payment initialization failed');
            }
        } catch (error) {
            setIsProcessing(false);
            console.error('Error:', error);
            alert('Payment initialization failed. Please try again.');
        }
    };

    const quickAmounts = [10, 25, 50, 100, 250, 500];

    return (
        <>
            <Head title="Add Funds">
                <script src="https://js.paystack.co/v2/inline.js"></script>
            </Head>

            <AppLayout breadcrumbs={breadcrumbs}>
                <div className="mx-auto max-w-2xl space-y-6">
                    {/* Header */}
                    <div className="flex items-center gap-4">
                        <Button variant="ghost" size="sm" asChild>
                            <a href="/wallet/balance">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Wallet
                            </a>
                        </Button>
                    </div>

                    <div className="space-y-2">
                        <h1 className="text-3xl font-bold">Add Funds</h1>
                        <p className="text-muted-foreground">Add money to your wallet to bid on projects and make payments</p>
                    </div>

                    {/* Current Balance */}
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Current Balance</p>
                                    <p className="text-2xl font-bold">₵{Number(auth.user.wallet_balance || 0).toFixed(2)}</p>
                                </div>
                                <div className="rounded-full bg-primary/10 p-3">
                                    <DollarSign className="h-6 w-6 text-primary" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Add Funds Form */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <CreditCard className="h-5 w-5" />
                                Add Funds
                            </CardTitle>
                            <CardDescription>
                                Choose an amount to add to your wallet. Payments are processed securely through Paystack.
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Quick Amount Selection */}
                                <div className="space-y-3">
                                    <Label>Quick Select</Label>
                                    <div className="grid grid-cols-3 gap-3">
                                        {quickAmounts.map((quickAmount) => (
                                            <Button
                                                key={quickAmount}
                                                type="button"
                                                variant={amount === quickAmount.toString() ? 'default' : 'outline'}
                                                onClick={() => setAmount(quickAmount.toString())}
                                                className="h-12"
                                            >
                                                ₵{quickAmount}
                                            </Button>
                                        ))}
                                    </div>
                                </div>

                                <Separator />

                                {/* Custom Amount */}
                                <div className="space-y-2">
                                    <Label htmlFor="amount">Custom Amount</Label>
                                    <div className="relative">
                                        <span className="absolute top-1/2 left-3 -translate-y-1/2 text-muted-foreground">₵</span>
                                        <Input
                                            id="amount"
                                            type="number"
                                            step="0.01"
                                            min="1"
                                            max="1000000"
                                            placeholder="0.00"
                                            value={amount}
                                            onChange={(e) => setAmount(e.target.value)}
                                            className="pl-8"
                                            required
                                        />
                                    </div>
                                </div>

                                {/* Email */}
                                <div className="space-y-2">
                                    <Label htmlFor="email">Email Address</Label>
                                    <Input id="email" type="email" value={email} onChange={(e) => setEmail(e.target.value)} required />
                                </div>

                                {/* Security Notice */}
                                <div className="rounded-lg bg-muted p-4">
                                    <div className="flex items-start gap-3">
                                        <Shield className="mt-0.5 h-5 w-5 text-primary" />
                                        <div className="space-y-1">
                                            <p className="text-sm font-medium">Secure Payment</p>
                                            <p className="text-xs text-muted-foreground">
                                                Your payment is processed securely through Paystack. We never store your card details.
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                {/* Submit Button */}
                                <Button type="submit" className="w-full" size="lg" disabled={isProcessing || !amount || parseFloat(amount) < 1}>
                                    {isProcessing ? (
                                        <>Processing...</>
                                    ) : (
                                        <>
                                            <CreditCard className="mr-2 h-4 w-4" />
                                            Add ₵{amount || '0.00'} to Wallet
                                        </>
                                    )}
                                </Button>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </AppLayout>
        </>
    );
}
