<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ClearInvalidSessions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sessions:clear-invalid-uuids';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear sessions that contain invalid user IDs (non-UUID format)';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Clearing sessions with invalid user IDs...');

        try {
            // Count sessions before clearing
            $totalSessions = DB::table('sessions')->count();
            $this->info("Found {$totalSessions} total sessions");

            // Clear sessions where user_id is not a valid UUID
            $cleared = DB::table('sessions')
                ->where('user_id', '!=', null)
                ->where(function ($query) {
                    $query->where('user_id', 'NOT LIKE', '%-%-%-%-%')
                        ->orWhere(DB::raw('CHAR_LENGTH(CAST(user_id AS TEXT))'), '!=', 36);
                })
                ->delete();

            $this->info("Cleared {$cleared} sessions with invalid user IDs");

            // Also clear sessions that might have serialized data with integer user IDs
            $payloadCleared = DB::table('sessions')
                ->where('payload', 'LIKE', '%"id";i:%')
                ->delete();

            $this->info("Cleared {$payloadCleared} sessions with serialized integer user IDs");

            $remainingSessions = DB::table('sessions')->count();
            $this->info("Remaining sessions: {$remainingSessions}");

            $this->info('Invalid sessions cleared successfully!');

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Failed to clear invalid sessions: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
