import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, XCircle } from 'lucide-react';

interface User {
    id: string;
    name: string;
    email: string;
    role: 'user' | 'admin' | 'super_admin';
    status: 'active' | 'suspended' | 'blocked';
    is_verified: boolean;
    wallet_balance: number;
    phone?: string;
    location?: string;
    bio?: string;
    skills?: string[];
    education?: string;
    created_at: string;
    projects: any[];
    bids: any[];
    wallet_transactions: any[];
}

interface UserStats {
    total_projects: number;
    total_bids: number;
    total_spent: number;
    total_earned: number;
    average_rating: number;
    total_ratings: number;
}

interface UserStatusCardProps {
    user: User;
    user_stats: UserStats;
    onToggleVerification: () => void;
}

export default function UserStatusCard({ user, user_stats, onToggleVerification }: UserStatusCardProps) {
    const getRoleColor = (role: string) => {
        switch (role) {
            case 'super_admin':
                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
            case 'admin':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'active':
                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
            case 'suspended':
                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
            case 'blocked':
                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
        }
    };

    return (
        <div className="space-y-6">
            {/* Status & Role */}
            <Card>
                <CardHeader>
                    <CardTitle>Status & Permissions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Role</span>
                        <Badge className={getRoleColor(user.role)}>{user.role.replace('_', ' ').toUpperCase()}</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Status</span>
                        <Badge className={getStatusColor(user.status)}>{user.status.toUpperCase()}</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Verified</span>
                        <Button variant="ghost" size="sm" onClick={onToggleVerification} className="h-auto p-0">
                            {user.is_verified ? <CheckCircle className="h-5 w-5 text-green-600" /> : <XCircle className="h-5 w-5 text-red-600" />}
                        </Button>
                    </div>
                    <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Wallet Balance</span>
                        <span className="text-sm font-bold">₵{user.wallet_balance}</span>
                    </div>
                </CardContent>
            </Card>

            {/* Stats */}
            <Card>
                <CardHeader>
                    <CardTitle>Statistics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                        <div className="rounded-lg bg-muted p-3 text-center">
                            <p className="text-2xl font-bold">{user_stats.total_projects}</p>
                            <p className="text-xs text-muted-foreground">Projects</p>
                        </div>
                        <div className="rounded-lg bg-muted p-3 text-center">
                            <p className="text-2xl font-bold">{user_stats.total_bids}</p>
                            <p className="text-xs text-muted-foreground">Bids</p>
                        </div>
                        <div className="rounded-lg bg-muted p-3 text-center">
                            <p className="text-2xl font-bold">₵{user_stats.total_spent}</p>
                            <p className="text-xs text-muted-foreground">Spent</p>
                        </div>
                        <div className="rounded-lg bg-muted p-3 text-center">
                            <p className="text-2xl font-bold">₵{user_stats.total_earned}</p>
                            <p className="text-xs text-muted-foreground">Earned</p>
                        </div>
                    </div>
                    {user_stats.total_ratings > 0 && (
                        <div className="rounded-lg bg-muted p-3 text-center">
                            <div className="flex items-center justify-center space-x-1">
                                <span className="text-lg font-bold">{user_stats.average_rating.toFixed(1)}</span>
                            </div>
                            <p className="text-xs text-muted-foreground">
                                {user_stats.total_ratings} rating{user_stats.total_ratings !== 1 ? 's' : ''}
                            </p>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
